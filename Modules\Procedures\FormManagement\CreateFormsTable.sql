-- Create Forms Table
-- Creates the forms table if it doesn't exist
CREATE TABLE IF NOT EXISTS forms (
    form_id SERIAL PRIMARY KEY,
    form_name VARCHAR(255) NOT NULL UNIQUE,
    display_name VARCHAR(255),
    category VARCHAR(100),
    is_active BOOLEAN DEFAULT true,
    last_modified TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_forms_name ON forms(form_name);
CREATE INDEX IF NOT EXISTS idx_forms_category ON forms(category);
CREATE INDEX IF NOT EXISTS idx_forms_active ON forms(is_active);

-- Insert some sample data if table is empty
INSERT INTO forms (form_name, display_name, category, is_active)
SELECT 'FormManagement', 'Form Management', 'Administration', true
WHERE NOT EXISTS (SELECT 1 FROM forms WHERE form_name = 'FormManagement');

INSERT INTO forms (form_name, display_name, category, is_active)
SELECT 'UserManagement', 'User Management', 'Administration', true
WHERE NOT EXISTS (SELECT 1 FROM forms WHERE form_name = 'UserManagement');

INSERT INTO forms (form_name, display_name, category, is_active)
SELECT 'PermissionManagement', 'Permission Management', 'Administration', true
WHERE NOT EXISTS (SELECT 1 FROM forms WHERE form_name = 'PermissionManagement');

INSERT INTO forms (form_name, display_name, category, is_active)
SELECT 'EstimateForm', 'Estimate Management', 'Business', true
WHERE NOT EXISTS (SELECT 1 FROM forms WHERE form_name = 'EstimateForm');

INSERT INTO forms (form_name, display_name, category, is_active)
SELECT 'ParametersForm', 'Parameters', 'Configuration', true
WHERE NOT EXISTS (SELECT 1 FROM forms WHERE form_name = 'ParametersForm');
