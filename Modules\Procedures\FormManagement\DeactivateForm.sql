-- Set transaction isolation level before the procedural block
SET TRANSACTION ISOLATION LEVEL SERIALIZABLE;

-- Soft Delete (Deactivate) Form
-- Deactivates a form instead of deleting it
DO $$
DECLARE
    existing_count INTEGER;
    updated_count INTEGER;
BEGIN
    -- Validate form ID
    IF @form_id IS NULL OR @form_id <= 0 THEN
        RAISE EXCEPTION 'Valid form ID is required';
    END IF;
    
    -- Check if form exists
    SELECT COUNT(*) INTO existing_count
    FROM forms 
    WHERE form_id = @form_id;
    
    IF existing_count = 0 THEN
        RAISE EXCEPTION 'Form with ID % does not exist', @form_id;
    END IF;
    
    -- Check if form is already inactive
    SELECT COUNT(*) INTO existing_count
    FROM forms 
    WHERE form_id = @form_id AND is_active = false;
    
    IF existing_count > 0 THEN
        RAISE EXCEPTION 'Form is already deactivated';
    END IF;
    
    -- Deactivate the form
    UPDATE forms SET
        is_active = false,
        last_modified = CURRENT_TIMESTAMP
    WHERE form_id = @form_id;
    
    GET DIAGNOSTICS updated_count = ROW_COUNT;
    
    -- Return success message
    SELECT 
        @form_id as form_id,
        'Form deactivated successfully' as message;
    
EXCEPTION
    WHEN OTHERS THEN
        RAISE EXCEPTION 'Error deactivating form: %', SQLERRM;
END $$;
