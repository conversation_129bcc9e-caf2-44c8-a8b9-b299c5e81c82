# Form Management Deletion Debugging Guide

## 🚨 Issue: "column 'form_id' does not exist" Error

This guide provides step-by-step debugging for the form deletion error.

## 🔍 Phase 1: Database Structure Verification

### Step 1: Run Database Verification Queries

Execute these queries directly in your PostgreSQL client to verify the database structure:

```sql
-- Check if forms table exists
SELECT EXISTS (
    SELECT FROM information_schema.tables
    WHERE table_schema = 'public'
    AND table_name = 'forms'
) as table_exists;

-- Get complete table structure
SELECT 
    column_name,
    data_type,
    is_nullable,
    column_default,
    ordinal_position
FROM information_schema.columns 
WHERE table_schema = 'public'
  AND table_name = 'forms'
ORDER BY ordinal_position;

-- Check specifically for form_id column
SELECT column_name, data_type 
FROM information_schema.columns 
WHERE table_schema = 'public'
  AND table_name = 'forms'
  AND column_name = 'form_id';
```

### Expected Results:
- **table_exists**: Should return `true`
- **Table structure**: Should show columns: form_id, form_name, display_name, category, is_active, last_modified
- **form_id column**: Should exist with data_type 'integer'

## 🔍 Phase 2: Application-Level Debugging

### Step 2: Test Form Deletion in Application

1. **Open Form Management**
2. **Select a form** by checking its checkbox
3. **Click Delete button**

The enhanced debugging will now:
- ✅ Verify table structure before deletion
- ✅ Test the specific form before actual deletion
- ✅ Show detailed error messages if issues are found

### Step 3: Check Debug Output

Look for these debug messages in the application output:

```
=== Delete button clicked ===
Verifying forms table structure...
Table verification result: SUCCESS: Table exists with columns: form_id (integer), form_name (character varying), ...
Testing deletion for form ID: [ID], Name: [Name]
Test deletion result: SUCCESS: Found form [ID] - [Name] - Form found for deletion
```

## 🔍 Phase 3: Manual Database Testing

### Step 4: Test Parameter Binding

Execute this query manually with a real form ID:

```sql
-- Replace [FORM_ID] with an actual form ID from your database
SELECT 
    form_id,
    form_name,
    'Form found for deletion' as status
FROM forms 
WHERE form_id = [FORM_ID];
```

### Step 5: Test Simple Delete

If the above works, test a simple delete:

```sql
-- Test delete (replace [FORM_ID] with actual ID)
BEGIN;
DELETE FROM forms WHERE form_id = [FORM_ID];
ROLLBACK; -- This prevents actual deletion
```

## 🔧 Common Issues and Solutions

### Issue 1: Table Doesn't Exist
**Symptoms**: `table_exists` returns `false`
**Solution**: Run the table creation script:
```sql
-- Execute: Modules/Procedures/FormManagement/CreateFormsTable.sql
```

### Issue 2: Wrong Database/Schema
**Symptoms**: Table exists but queries fail
**Solution**: Check connection string points to correct database

### Issue 3: Case Sensitivity
**Symptoms**: Column exists but not found
**Solution**: PostgreSQL is case-sensitive. Ensure column names are lowercase.

### Issue 4: Parameter Binding Issue
**Symptoms**: Manual queries work, but application fails
**Solution**: Check QueryExecutor parameter handling

## 🎯 Quick Diagnostic Commands

Run these in your PostgreSQL client for immediate diagnosis:

```sql
-- 1. List all tables
SELECT table_name FROM information_schema.tables WHERE table_schema = 'public';

-- 2. Show forms table structure
\d forms

-- 3. Count forms
SELECT COUNT(*) FROM forms;

-- 4. Show sample data
SELECT * FROM forms LIMIT 3;
```

## 📋 Debugging Checklist

- [ ] Forms table exists in database
- [ ] form_id column exists and is integer type
- [ ] Connection string points to correct database
- [ ] Sample forms data exists
- [ ] Manual SELECT with form_id works
- [ ] Application table verification passes
- [ ] Application test deletion passes

## 🚀 Next Steps

1. **If table doesn't exist**: Run CreateFormsTable.sql
2. **If table exists but wrong structure**: Check if you're connected to the right database
3. **If structure is correct**: The issue is in parameter binding - check QueryExecutor
4. **If all checks pass**: The issue may be in the DO $$ block syntax

## 📞 Support Information

If all debugging steps pass but deletion still fails, provide:
- Database version (PostgreSQL version)
- Connection string (without credentials)
- Complete error message
- Results of all verification queries
