// FormManagement Model - Data model for Form Management operations
// Usage: Represents form data with validation and business rules

using System;
using System.ComponentModel.DataAnnotations;
using System.Diagnostics;
using System.Text.RegularExpressions;

namespace ProManage.Modules.Models.FormManagement
{
    /// <summary>
    /// Data model representing a form in the system
    /// Provides validation and business rules for form management operations
    /// </summary>
    public class FormManagementModel
    {
        #region Properties

        /// <summary>
        /// Unique identifier for the form (auto-generated)
        /// </summary>
        public int FormId { get; set; }

        /// <summary>
        /// Internal form name - must be unique and follow naming conventions
        /// Required field, alphanumeric with underscores only
        /// </summary>
        [Required(ErrorMessage = "Form name is required")]
        [StringLength(255, ErrorMessage = "Form name cannot exceed 255 characters")]
        public string FormName { get; set; }

        /// <summary>
        /// User-friendly display name for the form
        /// Optional field for better user experience
        /// </summary>
        [StringLength(255, ErrorMessage = "Display name cannot exceed 255 characters")]
        public string DisplayName { get; set; }

        /// <summary>
        /// Category for grouping forms (e.g., Administration, Master, Utilities)
        /// Optional field for organization
        /// </summary>
        [StringLength(100, ErrorMessage = "Category cannot exceed 100 characters")]
        public string Category { get; set; }

        /// <summary>
        /// Indicates whether the form is active/enabled
        /// Defaults to true for new forms
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// Timestamp of last modification (auto-managed by database)
        /// </summary>
        public DateTime LastModified { get; set; }

        #endregion

        #region Constructors

        /// <summary>
        /// Default constructor for new form creation
        /// </summary>
        public FormManagementModel()
        {
            IsActive = true;
            LastModified = DateTime.Now;
        }

        /// <summary>
        /// Constructor with required fields
        /// </summary>
        /// <param name="formName">Internal form name</param>
        /// <param name="displayName">User-friendly display name</param>
        /// <param name="category">Form category</param>
        public FormManagementModel(string formName, string displayName = null, string category = null)
        {
            FormName = formName?.Trim();
            DisplayName = string.IsNullOrWhiteSpace(displayName) ? null : displayName.Trim();
            Category = string.IsNullOrWhiteSpace(category) ? null : category.Trim();
            IsActive = true;
            LastModified = DateTime.Now;
        }

        /// <summary>
        /// Full constructor for loading existing forms
        /// </summary>
        /// <param name="formId">Form ID</param>
        /// <param name="formName">Internal form name</param>
        /// <param name="displayName">User-friendly display name</param>
        /// <param name="category">Form category</param>
        /// <param name="isActive">Active status</param>
        /// <param name="lastModified">Last modification timestamp</param>
        public FormManagementModel(int formId, string formName, string displayName, string category, bool isActive, DateTime lastModified)
        {
            FormId = formId;
            FormName = formName?.Trim();
            DisplayName = string.IsNullOrWhiteSpace(displayName) ? null : displayName.Trim();
            Category = string.IsNullOrWhiteSpace(category) ? null : category.Trim();
            IsActive = isActive;
            LastModified = lastModified;
        }

        #endregion

        #region Validation Methods

        /// <summary>
        /// Validates the form name according to business rules
        /// </summary>
        /// <returns>True if valid, false otherwise</returns>
        public bool IsFormNameValid()
        {
            if (string.IsNullOrWhiteSpace(FormName))
                return false;

            // Must start with letter and contain only letters, numbers, and underscores
            var regex = new Regex(@"^[A-Za-z][A-Za-z0-9_]*$");
            return regex.IsMatch(FormName.Trim());
        }

        /// <summary>
        /// Gets validation error message for form name
        /// </summary>
        /// <returns>Error message or null if valid</returns>
        public string GetFormNameValidationError()
        {
            if (string.IsNullOrWhiteSpace(FormName))
                return "Form name is required";

            if (FormName.Trim().Length > 255)
                return "Form name cannot exceed 255 characters";

            if (!IsFormNameValid())
                return "Form name must start with a letter and contain only letters, numbers, and underscores";

            return null;
        }

        /// <summary>
        /// Validates all model properties
        /// </summary>
        /// <returns>True if all validations pass</returns>
        public bool IsValid()
        {
            return IsFormNameValid() &&
                   (string.IsNullOrEmpty(DisplayName) || DisplayName.Length <= 255) &&
                   (string.IsNullOrEmpty(Category) || Category.Length <= 100);
        }

        /// <summary>
        /// Gets all validation errors for the model
        /// </summary>
        /// <returns>Array of validation error messages</returns>
        public string[] GetValidationErrors()
        {
            var errors = new System.Collections.Generic.List<string>();

            var formNameError = GetFormNameValidationError();
            if (!string.IsNullOrEmpty(formNameError))
                errors.Add(formNameError);

            if (!string.IsNullOrEmpty(DisplayName) && DisplayName.Length > 255)
                errors.Add("Display name cannot exceed 255 characters");

            if (!string.IsNullOrEmpty(Category) && Category.Length > 100)
                errors.Add("Category cannot exceed 100 characters");

            return errors.ToArray();
        }

        #endregion

        #region Helper Methods

        /// <summary>
        /// Cleans and normalizes the model data
        /// </summary>
        public void Normalize()
        {
            FormName = FormName?.Trim();
            DisplayName = string.IsNullOrWhiteSpace(DisplayName) ? null : DisplayName.Trim();
            Category = string.IsNullOrWhiteSpace(Category) ? null : Category.Trim();
        }

        /// <summary>
        /// Creates a copy of the current model
        /// </summary>
        /// <returns>New instance with same values</returns>
        public FormManagementModel Clone()
        {
            return new FormManagementModel(FormId, FormName, DisplayName, Category, IsActive, LastModified);
        }

        /// <summary>
        /// Compares two models for equality (excluding LastModified)
        /// </summary>
        /// <param name="other">Model to compare with</param>
        /// <returns>True if models are equal</returns>
        public bool Equals(FormManagementModel other)
        {
            if (other == null) return false;

            return FormId == other.FormId &&
                   string.Equals(FormName, other.FormName, StringComparison.OrdinalIgnoreCase) &&
                   string.Equals(DisplayName, other.DisplayName, StringComparison.Ordinal) &&
                   string.Equals(Category, other.Category, StringComparison.Ordinal) &&
                   IsActive == other.IsActive;
        }

        /// <summary>
        /// Returns a string representation of the form
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            var displayText = !string.IsNullOrEmpty(DisplayName) ? DisplayName : FormName;
            var status = IsActive ? "Active" : "Inactive";
            var categoryText = !string.IsNullOrEmpty(Category) ? $" ({Category})" : "";
            
            return $"{displayText}{categoryText} - {status}";
        }

        #endregion

        #region Static Helper Methods

        /// <summary>
        /// Creates a new model from database row data
        /// </summary>
        /// <param name="row">DataRow containing form data</param>
        /// <returns>New FormManagementModel instance</returns>
        public static FormManagementModel FromDataRow(System.Data.DataRow row)
        {
            if (row == null) 
            {
                Debug.WriteLine("WARNING: FromDataRow called with null row");
                return null;
            }

            try
            {
                var formId = Convert.ToInt32(row["form_id"]);
                var formName = row["form_name"]?.ToString();
                var displayName = row["display_name"]?.ToString();
                var category = row["category"]?.ToString();
                var isActive = Convert.ToBoolean(row["is_active"]);
                var lastModified = Convert.ToDateTime(row["last_modified"]);

                Debug.WriteLine($"FromDataRow: Creating model for ID={formId}, Name='{formName}'");

                return new FormManagementModel(
                    formId,
                    formName,
                    displayName,
                    category,
                    isActive,
                    lastModified
                );
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"ERROR in FromDataRow: {ex.Message}");
                Debug.WriteLine($"Row data: form_id={row["form_id"]}, form_name={row["form_name"]}");
                return null;
            }
        }

        /// <summary>
        /// Validates form name format without creating an instance
        /// </summary>
        /// <param name="formName">Form name to validate</param>
        /// <returns>True if valid format</returns>
        public static bool IsValidFormNameFormat(string formName)
        {
            if (string.IsNullOrWhiteSpace(formName))
                return false;

            var regex = new Regex(@"^[A-Za-z][A-Za-z0-9_]*$");
            return regex.IsMatch(formName.Trim());
        }

        #endregion
    }

    /// <summary>
    /// Enumeration for form status filtering
    /// </summary>
    public enum FormStatus
    {
        All,
        Active,
        Inactive
    }

    /// <summary>
    /// Model for form search and filtering criteria
    /// </summary>
    public class FormSearchCriteria
    {
        public string FormNameFilter { get; set; }
        public string DisplayNameFilter { get; set; }
        public string CategoryFilter { get; set; }
        public FormStatus StatusFilter { get; set; } = FormStatus.All;
        public string SearchText { get; set; }

        /// <summary>
        /// Checks if any filters are applied
        /// </summary>
        public bool HasFilters => 
            !string.IsNullOrWhiteSpace(FormNameFilter) ||
            !string.IsNullOrWhiteSpace(DisplayNameFilter) ||
            !string.IsNullOrWhiteSpace(CategoryFilter) ||
            !string.IsNullOrWhiteSpace(SearchText) ||
            StatusFilter != FormStatus.All;
    }
}
