﻿<?xml version="1.0" encoding="utf-8"?>
<configuration>
    <configSections>
        <sectionGroup name="applicationSettings" type="System.Configuration.ApplicationSettingsGroup, System">
            <section name="DevExpress.LookAndFeel.Design.AppSettings" type="System.Configuration.ClientSettingsSection, System, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" requirePermission="false" />
        </sectionGroup>
        <sectionGroup name="userSettings" type="System.Configuration.UserSettingsGroup, System, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
            <section name="ProManage.Properties.Settings" type="System.Configuration.ClientSettingsSection, System, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" allowExeDefinition="MachineToLocalUser" requirePermission="false" />
        </sectionGroup>
    </configSections>
    
    <!-- Runtime Assembly Binding Redirects -->
    <runtime>
        <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
            <!-- Redirect Microsoft.EE.AsyncInterface to Microsoft.Bcl.AsyncInterfaces -->
            <dependentAssembly>
                <assemblyIdentity name="Microsoft.EE.AsyncInterface" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />
                <bindingRedirect oldVersion="0.0.0.0-6.0.0.0" newVersion="6.0.0.0" />
                <codeBase version="6.0.0.0" href="packages\Microsoft.Bcl.AsyncInterfaces.6.0.0\lib\net461\Microsoft.Bcl.AsyncInterfaces.dll" />            </dependentAssembly>
            
            <!-- Add redirect for Microsoft.Bcl.AsyncInterfaces if needed -->
            <dependentAssembly>
                <assemblyIdentity name="Microsoft.Bcl.AsyncInterfaces" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
                <bindingRedirect oldVersion="0.0.0.0-9.0.0.5" newVersion="9.0.0.5" />
            </dependentAssembly>
            
            <!-- Add redirect for System.Resources.Extensions -->            <dependentAssembly>
                <assemblyIdentity name="System.Resources.Extensions" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
                <bindingRedirect oldVersion="0.0.0.0-8.0.0.0" newVersion="8.0.0.0" />
                <codeBase version="8.0.0.0" href="System.Resources.Extensions.dll" />
            </dependentAssembly>
            
            <!-- DevExpress binding redirects -->
            <dependentAssembly>
                <assemblyIdentity name="DevExpress.Data.v24.1" publicKeyToken="b88d1754d700e49a" culture="neutral" />
                <bindingRedirect oldVersion="0.0.0.0-24.1.7.0" newVersion="24.1.7.0" />
            </dependentAssembly>
            <dependentAssembly>
                <assemblyIdentity name="DevExpress.Drawing.v24.1" publicKeyToken="b88d1754d700e49a" culture="neutral" />
                <bindingRedirect oldVersion="0.0.0.0-24.1.7.0" newVersion="24.1.7.0" />
            </dependentAssembly>
            <dependentAssembly>
                <assemblyIdentity name="DevExpress.XtraBars.v24.1" publicKeyToken="b88d1754d700e49a" culture="neutral" />
                <bindingRedirect oldVersion="0.0.0.0-24.1.7.0" newVersion="24.1.7.0" />
            </dependentAssembly>
            <!-- Add redirects for XtraReports -->
            <dependentAssembly>
                <assemblyIdentity name="DevExpress.XtraReports.v24.1" publicKeyToken="b88d1754d700e49a" culture="neutral" />
                <bindingRedirect oldVersion="0.0.0.0-24.1.7.0" newVersion="24.1.7.0" />
            </dependentAssembly>
            <dependentAssembly>
                <assemblyIdentity name="DevExpress.XtraReports.v24.1.Extensions" publicKeyToken="b88d1754d700e49a" culture="neutral" />
                <bindingRedirect oldVersion="0.0.0.0-24.1.7.0" newVersion="24.1.7.0" />
            </dependentAssembly>
            <dependentAssembly>
                <assemblyIdentity name="DevExpress.PivotGrid.v24.1.Core" publicKeyToken="b88d1754d700e49a" culture="neutral" />
                <bindingRedirect oldVersion="0.0.0.0-24.1.7.0" newVersion="24.1.7.0" />
            </dependentAssembly>
            <dependentAssembly>
                <assemblyIdentity name="System.Text.Json" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
                <bindingRedirect oldVersion="0.0.0.0-9.0.0.5" newVersion="9.0.0.5" />
            </dependentAssembly>
            <dependentAssembly>
                <assemblyIdentity name="System.Buffers" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
                <bindingRedirect oldVersion="0.0.0.0-4.0.3.0" newVersion="4.0.3.0" />
            </dependentAssembly>
            <dependentAssembly>
                <assemblyIdentity name="System.Runtime.CompilerServices.Unsafe" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />
                <bindingRedirect oldVersion="0.0.0.0-6.0.0.0" newVersion="6.0.0.0" />
            </dependentAssembly>
        </assemblyBinding>
    </runtime>
    
    <applicationSettings>
        <DevExpress.LookAndFeel.Design.AppSettings>
            <setting name="DefaultAppSkin" serializeAs="String">
                <value>Skin/Office 2019 Colorful</value>
            </setting>
            <setting name="DefaultPalette" serializeAs="String">
                <value></value>
            </setting>
            <setting name="TouchUI" serializeAs="String">
                <value></value>
            </setting>
            <setting name="CompactUI" serializeAs="String">
                <value></value>
            </setting>
            <setting name="TouchScaleFactor" serializeAs="String">
                <value></value>
            </setting>
            <setting name="DirectX" serializeAs="String">
                <value></value>
            </setting>
            <setting name="RegisterUserSkins" serializeAs="String">
                <value></value>
            </setting>
            <setting name="RegisterBonusSkins" serializeAs="String">
                <value></value>
            </setting>
            <setting name="FontBehavior" serializeAs="String">
                <value></value>
            </setting>
            <setting name="DefaultAppFont" serializeAs="String">
                <value>Segoe UI;9</value>
            </setting>
            <setting name="DPIAwarenessMode" serializeAs="String">
                <value></value>
            </setting>
            <setting name="CustomPaletteCollection" serializeAs="Xml">
                <value>
                    <CustomPaletteCollection xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" />
                </value>
            </setting>
        </DevExpress.LookAndFeel.Design.AppSettings>
    </applicationSettings>    <startup>
        <supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.8" />
    </startup>
    <appSettings>
        <add key="EnableWindowsFormsHighDpiAutoResizing" value="true" />
        <add key="UsePreserializedResources" value="true" />
        <add key="LoadResourceExtensions" value="true" />
    </appSettings>    <connectionStrings>
        <!-- Connection string will be populated when user configures database settings -->
        <!-- For testing purposes, using a local PostgreSQL instance -->
        <add name="MyConnection" connectionString="Host=localhost;Port=5432;Database=promanage_test;Username=********;Password=********;Timeout=30;CommandTimeout=30;" providerName="Npgsql" />
    </connectionStrings>
    <userSettings>
        <ProManage.Properties.Settings>
            <setting name="SidebarExpanded" serializeAs="String">
                <value>False</value>
            </setting>
            <setting name="Theme" serializeAs="String">
                <value>Office 2019 Colorful</value>
            </setting>
        </ProManage.Properties.Settings>
    </userSettings>
</configuration>