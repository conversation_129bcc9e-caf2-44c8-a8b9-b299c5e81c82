// FormManagement Validation - Business rules and validation for Form Management
// Usage: Provides comprehensive validation logic for form management operations

using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Text.RegularExpressions;
using ProManage.Modules.Data.FormManagement;
using ProManage.Modules.Models.FormManagement;

namespace ProManage.Modules.Validation.FormManagement
{
    /// <summary>
    /// Validation class for Form Management business rules
    /// Provides comprehensive validation for form operations
    /// </summary>
    public static class FormManagementValidation
    {
        #region Constants

        /// <summary>
        /// Maximum length for form name
        /// </summary>
        public const int MAX_FORM_NAME_LENGTH = 255;

        /// <summary>
        /// Maximum length for display name
        /// </summary>
        public const int MAX_DISPLAY_NAME_LENGTH = 255;

        /// <summary>
        /// Maximum length for category
        /// </summary>
        public const int MAX_CATEGORY_LENGTH = 100;

        /// <summary>
        /// Regular expression for valid form name format
        /// </summary>
        public const string FORM_NAME_PATTERN = @"^[A-Za-z][A-Za-z0-9_]*$";

        #endregion

        #region Validation Result Classes

        /// <summary>
        /// Represents the result of a validation operation
        /// </summary>
        public class ValidationResult
        {
            public bool IsValid { get; set; }
            public List<string> Errors { get; set; } = new List<string>();
            public List<string> Warnings { get; set; } = new List<string>();

            public void AddError(string error)
            {
                Errors.Add(error);
                IsValid = false;
            }

            public void AddWarning(string warning)
            {
                Warnings.Add(warning);
            }

            public string GetErrorsAsString()
            {
                return string.Join("\n", Errors);
            }

            public string GetWarningsAsString()
            {
                return string.Join("\n", Warnings);
            }
        }

        #endregion

        #region Form Name Validation

        /// <summary>
        /// Validates form name according to business rules
        /// </summary>
        /// <param name="formName">Form name to validate</param>
        /// <param name="excludeFormId">Form ID to exclude from uniqueness check</param>
        /// <returns>Validation result</returns>
        public static ValidationResult ValidateFormName(string formName, int? excludeFormId = null)
        {
            var result = new ValidationResult { IsValid = true };

            try
            {
                Debug.WriteLine($"=== ValidateFormName: {formName} ===");

                // Check if form name is provided
                if (string.IsNullOrWhiteSpace(formName))
                {
                    result.AddError("Form name is required");
                    return result;
                }

                // Trim and check again
                formName = formName.Trim();
                if (string.IsNullOrEmpty(formName))
                {
                    result.AddError("Form name cannot be empty or whitespace only");
                    return result;
                }

                // Check length
                if (formName.Length > MAX_FORM_NAME_LENGTH)
                {
                    result.AddError($"Form name cannot exceed {MAX_FORM_NAME_LENGTH} characters");
                }

                // Check format
                if (!Regex.IsMatch(formName, FORM_NAME_PATTERN))
                {
                    result.AddError("Form name must start with a letter and contain only letters, numbers, and underscores");
                }

                // Check for reserved names
                if (IsReservedFormName(formName))
                {
                    result.AddError($"'{formName}' is a reserved form name and cannot be used");
                }

                // Check uniqueness
                if (result.IsValid)
                {
                    if (!FormManagementRepository.IsFormNameAvailable(formName, excludeFormId))
                    {
                        result.AddError($"Form name '{formName}' already exists");
                    }
                }

                Debug.WriteLine($"Form name validation result: {result.IsValid}");
                return result;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in ValidateFormName: {ex.Message}");
                result.AddError($"Validation error: {ex.Message}");
                return result;
            }
        }

        /// <summary>
        /// Checks if a form name is reserved
        /// </summary>
        /// <param name="formName">Form name to check</param>
        /// <returns>True if reserved</returns>
        private static bool IsReservedFormName(string formName)
        {
            var reservedNames = new[]
            {
                "Form", "Control", "Window", "Dialog", "Panel", "Grid", "Button",
                "System", "Application", "Main", "Base", "Default", "Template",
                "Admin", "Root", "Master", "Super", "Global", "Config", "Settings"
            };

            return Array.Exists(reservedNames, name => 
                string.Equals(name, formName, StringComparison.OrdinalIgnoreCase));
        }

        #endregion

        #region Display Name Validation

        /// <summary>
        /// Validates display name
        /// </summary>
        /// <param name="displayName">Display name to validate</param>
        /// <returns>Validation result</returns>
        public static ValidationResult ValidateDisplayName(string displayName)
        {
            var result = new ValidationResult { IsValid = true };

            try
            {
                // Display name is optional
                if (string.IsNullOrWhiteSpace(displayName))
                {
                    return result;
                }

                // Check length
                if (displayName.Trim().Length > MAX_DISPLAY_NAME_LENGTH)
                {
                    result.AddError($"Display name cannot exceed {MAX_DISPLAY_NAME_LENGTH} characters");
                }

                // Check for potentially problematic characters
                if (displayName.Contains("\n") || displayName.Contains("\r") || displayName.Contains("\t"))
                {
                    result.AddWarning("Display name contains line breaks or tabs which may cause display issues");
                }

                return result;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in ValidateDisplayName: {ex.Message}");
                result.AddError($"Validation error: {ex.Message}");
                return result;
            }
        }

        #endregion

        #region Category Validation

        /// <summary>
        /// Validates category
        /// </summary>
        /// <param name="category">Category to validate</param>
        /// <returns>Validation result</returns>
        public static ValidationResult ValidateCategory(string category)
        {
            var result = new ValidationResult { IsValid = true };

            try
            {
                // Category is optional
                if (string.IsNullOrWhiteSpace(category))
                {
                    return result;
                }

                // Check length
                if (category.Trim().Length > MAX_CATEGORY_LENGTH)
                {
                    result.AddError($"Category cannot exceed {MAX_CATEGORY_LENGTH} characters");
                }

                // Suggest standard categories if not using common ones
                var standardCategories = new[] { "Administration", "Master", "Utilities", "Reports", "System" };
                if (!Array.Exists(standardCategories, cat => 
                    string.Equals(cat, category.Trim(), StringComparison.OrdinalIgnoreCase)))
                {
                    result.AddWarning($"Consider using a standard category: {string.Join(", ", standardCategories)}");
                }

                return result;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in ValidateCategory: {ex.Message}");
                result.AddError($"Validation error: {ex.Message}");
                return result;
            }
        }

        #endregion

        #region Complete Form Validation

        /// <summary>
        /// Validates an entire form model
        /// </summary>
        /// <param name="form">Form model to validate</param>
        /// <param name="isUpdate">True if this is an update operation</param>
        /// <returns>Validation result</returns>
        public static ValidationResult ValidateForm(FormManagementModel form, bool isUpdate = false)
        {
            var result = new ValidationResult { IsValid = true };

            try
            {
                Debug.WriteLine($"=== ValidateForm: {form?.FormName} (Update: {isUpdate}) ===");

                if (form == null)
                {
                    result.AddError("Form model cannot be null");
                    return result;
                }

                // Validate form name
                int? excludeId = isUpdate ? (int?)form.FormId : null;
                var formNameResult = ValidateFormName(form.FormName, excludeId);
                result.Errors.AddRange(formNameResult.Errors);
                result.Warnings.AddRange(formNameResult.Warnings);
                if (!formNameResult.IsValid)
                {
                    result.IsValid = false;
                }

                // Validate display name
                var displayNameResult = ValidateDisplayName(form.DisplayName);
                result.Errors.AddRange(displayNameResult.Errors);
                result.Warnings.AddRange(displayNameResult.Warnings);
                if (!displayNameResult.IsValid)
                {
                    result.IsValid = false;
                }

                // Validate category
                var categoryResult = ValidateCategory(form.Category);
                result.Errors.AddRange(categoryResult.Errors);
                result.Warnings.AddRange(categoryResult.Warnings);
                if (!categoryResult.IsValid)
                {
                    result.IsValid = false;
                }

                // Business rule validations
                ValidateBusinessRules(form, result, isUpdate);

                Debug.WriteLine($"Complete form validation result: {result.IsValid}");
                return result;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in ValidateForm: {ex.Message}");
                result.AddError($"Validation error: {ex.Message}");
                return result;
            }
        }

        /// <summary>
        /// Validates business rules for the form
        /// </summary>
        /// <param name="form">Form model</param>
        /// <param name="result">Validation result to update</param>
        /// <param name="isUpdate">True if update operation</param>
        private static void ValidateBusinessRules(FormManagementModel form, ValidationResult result, bool isUpdate)
        {
            try
            {
                // For updates, ensure form ID is valid
                if (isUpdate && form.FormId <= 0)
                {
                    result.AddError("Valid form ID is required for updates");
                }

                // Warn if deactivating a form that might be in use
                if (isUpdate && !form.IsActive)
                {
                    result.AddWarning("Deactivating this form may affect users who have access to it");
                }

                // Suggest display name if not provided
                if (string.IsNullOrWhiteSpace(form.DisplayName))
                {
                    result.AddWarning("Consider providing a display name for better user experience");
                }

                // Suggest category if not provided
                if (string.IsNullOrWhiteSpace(form.Category))
                {
                    result.AddWarning("Consider assigning a category for better organization");
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in ValidateBusinessRules: {ex.Message}");
                result.AddError($"Business rule validation error: {ex.Message}");
            }
        }

        #endregion

        #region Utility Methods

        /// <summary>
        /// Quick validation for form name format only
        /// </summary>
        /// <param name="formName">Form name to validate</param>
        /// <returns>True if format is valid</returns>
        public static bool IsValidFormNameFormat(string formName)
        {
            if (string.IsNullOrWhiteSpace(formName))
                return false;

            return Regex.IsMatch(formName.Trim(), FORM_NAME_PATTERN);
        }

        /// <summary>
        /// Gets suggested form name based on display name
        /// </summary>
        /// <param name="displayName">Display name</param>
        /// <returns>Suggested form name</returns>
        public static string SuggestFormName(string displayName)
        {
            if (string.IsNullOrWhiteSpace(displayName))
                return "";

            // Remove special characters and spaces, convert to PascalCase
            var suggestion = Regex.Replace(displayName.Trim(), @"[^A-Za-z0-9\s]", "");
            suggestion = Regex.Replace(suggestion, @"\s+", " ");
            
            var words = suggestion.Split(' ');
            var result = "";
            
            foreach (var word in words)
            {
                if (!string.IsNullOrEmpty(word))
                {
                    result += char.ToUpper(word[0]) + word.Substring(1).ToLower();
                }
            }

            // Ensure it starts with a letter
            if (!string.IsNullOrEmpty(result) && !char.IsLetter(result[0]))
            {
                result = "Form" + result;
            }

            return result;
        }

        #endregion
    }
}
