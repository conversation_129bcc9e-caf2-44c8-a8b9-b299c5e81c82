## ✅ Main Form Permission System - Finalized Sequential Plan (Developer-Friendly & Secure)

### ✅ GOAL
Ensure that only valid **Main Forms** (connected to Ribbon) are shown in permission form grid and synced with database.

### ✅ SYSTEM OVERVIEW
Define all main forms in a single registry, and use this registry to:
1. Populate permission grid
2. Drive Ribbon menu
3. Sync DB form list
4. Enforce security and maintainability best practices

### Note: the codes mentioned in files is just sample you can improvise and customize.
---

## 🔷 STEP 1: Create `MainFormRegistry.cs` (Single Source of Truth)

### ✅ What It Does
- Keeps your **visually designed Ribbon layout exactly as-is** (no code-based creation)
- Uses code to **hide buttons at runtime** if the user does not have read permission
- Preserves all icons, layout, and ribbon grouping from Visual Studio Designer

### ✅ How It Works
1. In Visual Studio Designer:
   - Design your full Ribbon using DevExpress designer
   - For each `BarButtonItem`, set its `Tag` property = matching form name used in permission table
     - Example: `Tag = "UserManagementForm"`

2. In the MDI parent form, add a method like this:

```
public static class MainFormRegistry
{
    public static List<FormDefinition> GetMainForms()
    {
        return new List<FormDefinition>
        {
            new FormDefinition("EstimateForm", typeof(EstimateForm)),
            new FormDefinition("ParametersForm", typeof(ParametersForm)),
            new FormDefinition("UserManagementForm", typeof(UserManagementForm))
        };
    }
}

public class FormDefinition
{
    public string Name { get; set; }
    public Type FormType { get; set; }

    public FormDefinition(string name, Type formType)
    {
        Name = name;
        FormType = formType;
    }
}
```

---

## 🔷 STEP 2: Use Registry to Load Forms in Permission Grid

### ✅ What It Does
- Loads forms into Role/User permission grids from the registry

### ✅ How It Works
```csharp
gridControl.DataSource = MainFormRegistry.GetMainForms();
```

### ✅ Why It Matters
- You get full design-time layout control using DevExpress tools
- Runtime logic still enforces permission rules
- No code duplication, and form opening logic remains clean

> ✅ Best of both worlds: **Designed visually, secured programmatically**
- Grid will always reflect correct forms
- Avoids manual mismatches and misconfigurations

---

## 🔷 STEP 3: Auto-Sync Registry with Database

### ✅ What It Does
- Syncs `MainFormRegistry` with `role_permission` and `user_permission` tables
- Inserts new forms with default (disabled) permissions for **all roles and all users**
- Deletes obsolete forms from both tables

### ✅ How It Works
- Fetch form list from registry
- Fetch distinct form names from database (`role_permission`, `user_permission`)
- For missing forms:
  - Fetch all role IDs and user IDs
  - Insert form rows for each role/user with all actions (read, new, edit, delete, print) = `false`
- For obsolete forms:
  - Delete all rows for those form names from both tables

```csharp

public class FormDiscoveryService
{
    public void SyncFormsWithDatabase()
    {
        var registryForms = MainFormRegistry.GetMainForms();
        var dbForms = GetFormNamesFromDatabase();

        var missingForms = registryForms.Where(f => !dbForms.Contains(f.Name));
        var obsoleteForms = dbForms.Where(name => !registryForms.Any(f => f.Name == name));

        var allRoleIds = GetAllRoleIds();
        var allUserIds = GetAllUserIds();

        foreach (var form in missingForms)
        {
            foreach (var roleId in allRoleIds)
                InsertRolePermission(roleId, form.Name, false, false, false, false, false);

            foreach (var userId in allUserIds)
                InsertUserPermission(userId, form.Name, false, false, false, false, false);
        }

        foreach (var formName in obsoleteForms)
        {
            DeleteFromRolePermissions(formName);
            DeleteFromUserPermissions(formName);
        }
    }

    // Helper methods (to be implemented using parameterized PostgreSQL commands)
    private List<string> GetFormNamesFromDatabase() => new();
    private List<int> GetAllRoleIds() => new();
    private List<int> GetAllUserIds() => new();
    private void InsertRolePermission(int roleId, string formName, bool read, bool add, bool edit, bool delete, bool print) { }
    private void InsertUserPermission(int userId, string formName, bool read, bool add, bool edit, bool delete, bool print) { }
    private void DeleteFromRolePermissions(string formName) { }
    private void DeleteFromUserPermissions(string formName) { }
}
```

### ✅ Why It Matters
- DB stays clean and aligned
- Ensures permission UI reflects only active, valid forms

> Security Tips:
> - Use parameterized queries
> - Restrict sync to admin context
> - Wrap changes in DB transaction for atomicity

### Note: I need the progress to be shown via lblStatus which is present in permission form. meaning it should show how many records "Reading Registry", "Read DB", "Syncing DB" etc.
---

## 🔷 STEP 4: Control Visibility of Designed Ribbon Buttons (Final Approach)

### ✅ What It Does
- Builds the DevExpress Ribbon menu based on `MainFormRegistry`
- Only includes forms the user has **Read** permission for
- Automatically links each button to its corresponding form

### ✅ How It Works
1. **Loop through all forms** in `MainFormRegistry.GetMainForms()`.
2. For each form, **check if the user has `Read` permission** (from DB or cache).
3. If allowed, **create a new `BarButtonItem`** using DevExpress controls.
4. Set:
   - `Caption` = form name (or friendly display name if added later)
   - `SvgImage` = DevExpress vector icon from `svgImageCollection` (e.g., `"document_new"`)
5. Hook `ItemClick` event to **open the form using reflection**:
   - `Activator.CreateInstance(form.FormType)`
   - Set `instance.MdiParent = this;`
   - Call `instance.Show();`
6. Finally, **add the button to a `RibbonPageGroup`**.

```csharp
foreach (BarItem item in ribbon.Items)
{
    if (item is BarButtonItem btn && btn.Tag is string formName)
    {
        if (!UserHasReadPermission(formName))
        {
            btn.Visibility = BarItemVisibility.Never;
        }
    }
};

    ribbonPageGroup.Items.Add(btn); // Or group by category if needed
}
```

### ✅ Why It Matters
- UI stays aligned with permission model
- Prevents unauthorized access to forms
- Supports clean, dynamic Ribbon creation

> ℹ️ If using **DevExpress built-in icons**, `IconPath` in registry isn't needed. You can directly assign SVG icons from your form’s `svgImageCollection`.
- UI stays aligned with permission model
- Prevents unauthorized access to forms

---

## ✅ Summary: Why This System Works

- ✔️ Developer-Friendly: One central list
- ✔️ Secure: Least privilege & read-checks
- ✔️ Maintainable: Easy to update and extend
- ✔️ Scalable: Metadata-ready and UI flexible
- ✔️ Testable: Fully verifiable via unit or CI tests
- ✔️ Clean: No obsolete forms in UI or DB

> 🔁 All layers (UI, Grid, DB) depend on the central registry → safe, consistent, future-proof.

Sure — here’s a clear checklist of the **manual jobs** you'll need to do as part of this permission system setup. These are **one-time or occasional setup/config tasks**, not runtime code logic.

---

## ✅ Manual Tasks User Must Perform

### 🔷 1. **Design Your DevExpress Ribbon (Already Started)**

* Use the Visual Studio **Designer** to build your Ribbon layout.
* Organize buttons under correct RibbonPages and RibbonPageGroups.
* Use DevExpress **built-in SVG icons** (from `svgImageCollection`).

### 🔷 2. **Set `Tag` Property for Each Button**

* For every `BarButtonItem` in the ribbon, set:

  ```
  Tag = "FormName"  // e.g., "UserManagementForm"
  ```
* This name must match exactly with:

  * The `form_name` in your `role_permission` / `user_permission` tables
  * The `Name` in your `MainFormRegistry`

> 💡 **Tip**: Use copy-paste from your `MainFormRegistry` list to avoid typos.

---

### 🔷 3. **Maintain `MainFormRegistry`**

* This file is your **source of truth** for:

  * Which forms are considered "Main Forms"
  * What forms will appear in the permission grid and be checked during sync

#### Manual work:

* Add a line for each main form you add to the project:

  ```csharp
  new FormDefinition("NewMainForm", typeof(NewMainForm))
  ```

---

## Optional But Recommended Manual Jobs

| Task                        | Description                                                                                   |
| --------------------------- | --------------------------------------------------------------------------------------------- |
| **Audit Tags**              | Periodically check that all Ribbon buttons have the correct `Tag` value matching a real form. |
| **Test Login Permissions**  | After assigning role/user permissions, log in with test users to verify visibility behavior.  |
| **Protect Ribbon Designer** | Optionally lock the designer code to prevent accidental removal of important tags.            |

