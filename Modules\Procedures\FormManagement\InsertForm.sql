-- Insert New Form
-- Creates a new form and returns the created record
-- Note: Validation is now handled in the C# application layer for better error handling

INSERT INTO forms (
    form_name,
    display_name,
    category,
    is_active,
    last_modified
) VALUES (
    TRIM(@form_name),
    CASE WHEN @display_name IS NULL OR TRIM(@display_name) = ''
         THEN NULL
         ELSE TRIM(@display_name)
    END,
    CASE WHEN @category IS NULL OR TRIM(@category) = ''
         THEN NULL
         ELSE TRIM(@category)
    END,
    COALESCE(@is_active, true),
    CURRENT_TIMESTAMP
) RETURNING
    form_id,
    form_name,
    display_name,
    category,
    is_active,
    last_modified;
