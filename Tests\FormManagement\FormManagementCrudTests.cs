using System;
using System.Collections.Generic;
using System.Data;
using System.Diagnostics;
using System.Linq;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using ProManage.Modules.Data.FormManagement;
using ProManage.Modules.Models.FormManagement;
using ProManage.Modules.Validation.FormManagement;
using ProManage.Modules.Helpers.FormManagement;
using ProManage.Modules.Connections;

namespace ProManage.Tests.FormManagement
{
    /// <summary>
    /// Comprehensive tests for FormManagement CRUD operations
    /// Tests Create, Read, Update, Delete functionality
    /// </summary>
    [TestClass]
    public class FormManagementCrudTests
    {
        private static string _testFormPrefix;
        private static List<int> _createdFormIds;        [ClassInitialize]
        public static void ClassInitialize(TestContext context)
        {
            // Initialize test environment
            _testFormPrefix = $"TestForm_{DateTime.Now:yyyyMMdd_HHmmss}";
            _createdFormIds = new List<int>();
            
            Debug.WriteLine($"=== FormManagement CRUD Tests Started: {DateTime.Now} ===");
            Debug.WriteLine($"Test prefix: {_testFormPrefix}");
            
            // Ensure database connection is available
            try
            {
                var connectionTest = DatabaseConnectionManager.Instance.GetConnection();
                Assert.IsNotNull(connectionTest, "Database connection should be available for tests");
                Debug.WriteLine("Database connection verified");
                
                // Ensure forms table exists by running the CreateFormsTable script
                SetupTestDatabase();
                Debug.WriteLine("Database setup completed");
            }
            catch (Exception ex)
            {
                Assert.Fail($"Database connection or setup failed: {ex.Message}");
            }
        }

        /// <summary>
        /// Sets up the test database by creating necessary tables
        /// </summary>
        private static void SetupTestDatabase()
        {
            try
            {
                // Execute the CreateFormsTable script to ensure the table exists
                var createTableResult = QueryExecutor.ExecuteQueryFromFile("FormManagement", "CreateFormsTable", out string errorMessage, null);
                
                if (!string.IsNullOrEmpty(errorMessage))
                {
                    Debug.WriteLine($"Warning during table creation: {errorMessage}");
                    // Don't fail here as the table might already exist
                }
                
                Debug.WriteLine("Forms table creation completed");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error setting up test database: {ex.Message}");
                throw new Exception($"Failed to setup test database: {ex.Message}", ex);
            }
        }

        [ClassCleanup]
        public static void ClassCleanup()
        {
            // Clean up any test forms that weren't properly deleted
            Debug.WriteLine("=== Cleaning up test forms ===");
            
            foreach (var formId in _createdFormIds.ToList())
            {
                try
                {
                    FormManagementRepository.DeleteForm(formId);
                    Debug.WriteLine($"Cleaned up test form ID: {formId}");
                }
                catch (Exception ex)
                {
                    Debug.WriteLine($"Failed to clean up form {formId}: {ex.Message}");
                }
            }
            
            Debug.WriteLine($"=== FormManagement CRUD Tests Completed: {DateTime.Now} ===");
        }

        [TestInitialize]
        public void TestInitialize()
        {
            Debug.WriteLine($"\n--- Test Method Starting: {TestContext.TestName} ---");
        }

        [TestCleanup]
        public void TestCleanup()
        {
            Debug.WriteLine($"--- Test Method Completed: {TestContext.TestName} ---\n");
        }

        public TestContext TestContext { get; set; }

        #region Create (Add) Tests

        [TestMethod]
        [TestCategory("FormManagement")]
        [TestCategory("CRUD")]
        [TestCategory("Create")]
        public void FormManagement_Create_ValidForm_ShouldSucceed()
        {
            // Arrange
            var testFormName = $"{_testFormPrefix}_Create_Valid";
            var newForm = new FormManagementModel
            {
                FormName = testFormName,
                DisplayName = "Test Form for Creation",
                Category = "Test",
                IsActive = true
            };

            FormManagementModel createdForm = null;

            try
            {
                // Act
                createdForm = FormManagementRepository.CreateForm(newForm);

                // Assert
                Assert.IsNotNull(createdForm, "Created form should not be null");
                Assert.IsTrue(createdForm.FormId > 0, "Created form should have valid ID");
                Assert.AreEqual(testFormName, createdForm.FormName, "Form name should match");
                Assert.AreEqual("Test Form for Creation", createdForm.DisplayName, "Display name should match");
                Assert.AreEqual("Test", createdForm.Category, "Category should match");
                Assert.IsTrue(createdForm.IsActive, "Form should be active");
                Assert.IsTrue(createdForm.LastModified > DateTime.Now.AddMinutes(-1), "Last modified should be recent");

                // Track for cleanup
                _createdFormIds.Add(createdForm.FormId);

                Debug.WriteLine($"Successfully created form with ID: {createdForm.FormId}");
            }
            catch (Exception ex)
            {
                Assert.Fail($"Form creation failed: {ex.Message}");
            }
        }

        [TestMethod]
        [TestCategory("FormManagement")]
        [TestCategory("CRUD")]
        [TestCategory("Create")]
        public void FormManagement_Create_DuplicateName_ShouldFail()
        {
            // Arrange
            var testFormName = $"{_testFormPrefix}_Create_Duplicate";
            var firstForm = new FormManagementModel
            {
                FormName = testFormName,
                DisplayName = "First Test Form",
                Category = "Test",
                IsActive = true
            };

            var duplicateForm = new FormManagementModel
            {
                FormName = testFormName, // Same name
                DisplayName = "Duplicate Test Form",
                Category = "Test",
                IsActive = true
            };

            FormManagementModel createdForm = null;

            try
            {
                // Act - Create first form
                createdForm = FormManagementRepository.CreateForm(firstForm);
                _createdFormIds.Add(createdForm.FormId);

                // Act & Assert - Try to create duplicate
                var exception = Assert.ThrowsException<ArgumentException>(() =>
                {
                    FormManagementRepository.CreateForm(duplicateForm);
                });

                Assert.IsTrue(exception.Message.Contains("already exists"), 
                    "Exception should indicate duplicate name issue");

                Debug.WriteLine($"Correctly prevented duplicate form creation: {exception.Message}");
            }
            catch (Exception ex)
            {
                if (createdForm != null)
                    _createdFormIds.Add(createdForm.FormId);
                Assert.Fail($"Unexpected error during duplicate test: {ex.Message}");
            }
        }

        [TestMethod]
        [TestCategory("FormManagement")]
        [TestCategory("CRUD")]
        [TestCategory("Create")]
        public void FormManagement_Create_InvalidFormName_ShouldFail()
        {
            // Arrange
            var invalidForm = new FormManagementModel
            {
                FormName = "123InvalidName", // Starts with number
                DisplayName = "Invalid Test Form",
                Category = "Test",
                IsActive = true
            };

            try
            {
                // Act & Assert
                var exception = Assert.ThrowsException<ArgumentException>(() =>
                {
                    FormManagementRepository.CreateForm(invalidForm);
                });

                Assert.IsTrue(exception.Message.Contains("must start with a letter"), 
                    "Exception should indicate invalid form name format");

                Debug.WriteLine($"Correctly rejected invalid form name: {exception.Message}");
            }
            catch (Exception ex)
            {
                Assert.Fail($"Unexpected error during invalid name test: {ex.Message}");
            }
        }

        #endregion

        #region Read Tests

        [TestMethod]
        [TestCategory("FormManagement")]
        [TestCategory("CRUD")]
        [TestCategory("Read")]
        public void FormManagement_Read_GetAllForms_ShouldReturnList()
        {
            try
            {
                // Act
                var allForms = FormManagementRepository.GetAllForms();

                // Assert
                Assert.IsNotNull(allForms, "Forms list should not be null");
                Assert.IsTrue(allForms.Count >= 0, "Forms list should be valid");

                Debug.WriteLine($"Successfully retrieved {allForms.Count} forms");

                // Verify structure of returned forms
                if (allForms.Count > 0)
                {
                    var firstForm = allForms.First();
                    Assert.IsTrue(firstForm.FormId > 0, "Form should have valid ID");
                    Assert.IsFalse(string.IsNullOrEmpty(firstForm.FormName), "Form should have name");
                }
            }
            catch (Exception ex)
            {
                Assert.Fail($"Failed to retrieve forms: {ex.Message}");
            }
        }

        [TestMethod]
        [TestCategory("FormManagement")]
        [TestCategory("CRUD")]
        [TestCategory("Read")]
        public void FormManagement_Read_GetFormById_ShouldReturnCorrectForm()
        {
            // Arrange - Create a test form first
            var testFormName = $"{_testFormPrefix}_Read_ById";
            var newForm = new FormManagementModel
            {
                FormName = testFormName,
                DisplayName = "Test Form for Read",
                Category = "Test",
                IsActive = true
            };

            FormManagementModel createdForm = null;

            try
            {
                // Arrange - Create the form
                createdForm = FormManagementRepository.CreateForm(newForm);
                _createdFormIds.Add(createdForm.FormId);

                // Act
                var retrievedForm = FormManagementRepository.GetFormById(createdForm.FormId);

                // Assert
                Assert.IsNotNull(retrievedForm, "Retrieved form should not be null");
                Assert.AreEqual(createdForm.FormId, retrievedForm.FormId, "Form IDs should match");
                Assert.AreEqual(testFormName, retrievedForm.FormName, "Form names should match");
                Assert.AreEqual("Test Form for Read", retrievedForm.DisplayName, "Display names should match");

                Debug.WriteLine($"Successfully retrieved form by ID: {retrievedForm.FormId}");
            }
            catch (Exception ex)
            {
                if (createdForm != null)
                    _createdFormIds.Add(createdForm.FormId);
                Assert.Fail($"Failed to retrieve form by ID: {ex.Message}");
            }
        }

        #endregion

        #region Update (Edit) Tests

        [TestMethod]
        [TestCategory("FormManagement")]
        [TestCategory("CRUD")]
        [TestCategory("Update")]
        public void FormManagement_Update_ValidChanges_ShouldSucceed()
        {
            // Arrange - Create a test form first
            var testFormName = $"{_testFormPrefix}_Update_Valid";
            var newForm = new FormManagementModel
            {
                FormName = testFormName,
                DisplayName = "Original Display Name",
                Category = "Original",
                IsActive = true
            };

            FormManagementModel createdForm = null;

            try
            {
                // Arrange - Create the form
                createdForm = FormManagementRepository.CreateForm(newForm);
                _createdFormIds.Add(createdForm.FormId);

                // Arrange - Prepare updates
                var updatedForm = new FormManagementModel
                {
                    FormId = createdForm.FormId,
                    FormName = createdForm.FormName, // Keep same name
                    DisplayName = "Updated Display Name",
                    Category = "Updated",
                    IsActive = false // Change status
                };

                // Act
                var resultForm = FormManagementRepository.UpdateForm(updatedForm);

                // Assert
                Assert.IsNotNull(resultForm, "Updated form should not be null");
                Assert.AreEqual(createdForm.FormId, resultForm.FormId, "Form ID should remain same");
                Assert.AreEqual(testFormName, resultForm.FormName, "Form name should remain same");
                Assert.AreEqual("Updated Display Name", resultForm.DisplayName, "Display name should be updated");
                Assert.AreEqual("Updated", resultForm.Category, "Category should be updated");
                Assert.IsFalse(resultForm.IsActive, "IsActive should be updated");
                Assert.IsTrue(resultForm.LastModified > createdForm.LastModified, "Last modified should be newer");

                Debug.WriteLine($"Successfully updated form ID: {resultForm.FormId}");
            }
            catch (Exception ex)
            {
                if (createdForm != null)
                    _createdFormIds.Add(createdForm.FormId);
                Assert.Fail($"Form update failed: {ex.Message}");
            }
        }

        [TestMethod]
        [TestCategory("FormManagement")]
        [TestCategory("CRUD")]
        [TestCategory("Update")]
        public void FormManagement_Update_NonExistentForm_ShouldFail()
        {
            // Arrange
            var nonExistentForm = new FormManagementModel
            {
                FormId = 999999, // Non-existent ID
                FormName = $"{_testFormPrefix}_Update_NonExistent",
                DisplayName = "Non-existent Form",
                Category = "Test",
                IsActive = true
            };

            try
            {
                // Act & Assert
                var exception = Assert.ThrowsException<Exception>(() =>
                {
                    FormManagementRepository.UpdateForm(nonExistentForm);
                });

                Assert.IsTrue(exception.Message.Contains("failed") || exception.Message.Contains("no data"), 
                    "Exception should indicate update failure");

                Debug.WriteLine($"Correctly handled non-existent form update: {exception.Message}");
            }
            catch (Exception ex)
            {
                Assert.Fail($"Unexpected error during non-existent form test: {ex.Message}");
            }
        }

        #endregion

        #region Delete Tests

        [TestMethod]
        [TestCategory("FormManagement")]
        [TestCategory("CRUD")]
        [TestCategory("Delete")]
        public void FormManagement_Delete_ExistingForm_ShouldSucceed()
        {
            // Arrange - Create a test form first
            var testFormName = $"{_testFormPrefix}_Delete_Valid";
            var newForm = new FormManagementModel
            {
                FormName = testFormName,
                DisplayName = "Test Form for Deletion",
                Category = "Test",
                IsActive = true
            };

            FormManagementModel createdForm = null;

            try
            {
                // Arrange - Create the form
                createdForm = FormManagementRepository.CreateForm(newForm);
                var formIdToDelete = createdForm.FormId;

                // Act
                var deleteResult = FormManagementRepository.DeleteForm(formIdToDelete);

                // Assert
                Assert.IsTrue(deleteResult, "Delete operation should return true");

                // Verify form is actually deleted
                var deletedForm = FormManagementRepository.GetFormById(formIdToDelete);
                Assert.IsNull(deletedForm, "Deleted form should not be retrievable");

                Debug.WriteLine($"Successfully deleted form ID: {formIdToDelete}");

                // Remove from cleanup list since it's already deleted
                _createdFormIds.Remove(formIdToDelete);
            }
            catch (Exception ex)
            {
                if (createdForm != null)
                    _createdFormIds.Add(createdForm.FormId);
                Assert.Fail($"Form deletion failed: {ex.Message}");
            }
        }

        [TestMethod]
        [TestCategory("FormManagement")]
        [TestCategory("CRUD")]
        [TestCategory("Delete")]
        public void FormManagement_Delete_NonExistentForm_ShouldHandleGracefully()
        {
            // Arrange
            var nonExistentFormId = 999999;

            try
            {
                // Act
                var deleteResult = FormManagementRepository.DeleteForm(nonExistentFormId);

                // Assert - Should handle gracefully (might return false or throw exception)
                // The exact behavior depends on implementation
                Debug.WriteLine($"Delete non-existent form result: {deleteResult}");
                
                // Test passes if no unhandled exception occurs
                Assert.IsTrue(true, "Delete operation should handle non-existent form gracefully");
            }
            catch (Exception ex)
            {
                // Acceptable if it throws a specific exception
                Debug.WriteLine($"Delete non-existent form threw exception: {ex.Message}");
                Assert.IsTrue(ex.Message.Contains("not found") || ex.Message.Contains("does not exist"), 
                    "Exception should indicate form not found");
            }
        }

        #endregion

        #region Integration Tests

        [TestMethod]
        [TestCategory("FormManagement")]
        [TestCategory("CRUD")]
        [TestCategory("Integration")]
        public void FormManagement_FullCrudCycle_ShouldWork()
        {
            // This test performs a complete CRUD cycle: Create -> Read -> Update -> Delete
            var testFormName = $"{_testFormPrefix}_FullCycle";
            FormManagementModel createdForm = null;

            try
            {
                // 1. CREATE
                var newForm = new FormManagementModel
                {
                    FormName = testFormName,
                    DisplayName = "Full Cycle Test Form",
                    Category = "Integration",
                    IsActive = true
                };

                createdForm = FormManagementRepository.CreateForm(newForm);
                Assert.IsNotNull(createdForm, "Form should be created successfully");
                Debug.WriteLine($"Step 1 - Created form ID: {createdForm.FormId}");

                // 2. READ
                var readForm = FormManagementRepository.GetFormById(createdForm.FormId);
                Assert.IsNotNull(readForm, "Form should be readable");
                Assert.AreEqual(createdForm.FormId, readForm.FormId, "Read form should match created form");
                Debug.WriteLine($"Step 2 - Read form successfully");

                // 3. UPDATE
                readForm.DisplayName = "Updated Full Cycle Test Form";
                readForm.Category = "Updated Integration";
                readForm.IsActive = false;

                var updatedForm = FormManagementRepository.UpdateForm(readForm);
                Assert.IsNotNull(updatedForm, "Form should be updated successfully");
                Assert.AreEqual("Updated Full Cycle Test Form", updatedForm.DisplayName, "Display name should be updated");
                Assert.IsFalse(updatedForm.IsActive, "Status should be updated");
                Debug.WriteLine($"Step 3 - Updated form successfully");

                // 4. DELETE
                var deleteResult = FormManagementRepository.DeleteForm(updatedForm.FormId);
                Assert.IsTrue(deleteResult, "Form should be deleted successfully");

                // Verify deletion
                var deletedForm = FormManagementRepository.GetFormById(updatedForm.FormId);
                Assert.IsNull(deletedForm, "Form should no longer exist after deletion");
                Debug.WriteLine($"Step 4 - Deleted form successfully");

                // Remove from cleanup since it's deleted
                createdForm = null;
            }
            catch (Exception ex)
            {
                if (createdForm != null)
                    _createdFormIds.Add(createdForm.FormId);
                Assert.Fail($"Full CRUD cycle failed: {ex.Message}");
            }
        }

        #endregion

        #region Validation Tests

        [TestMethod]
        [TestCategory("FormManagement")]
        [TestCategory("Validation")]
        public void FormManagement_Validation_FormNameUniqueness_ShouldWork()
        {
            // Test the form name validation logic
            var testFormName = $"{_testFormPrefix}_Validation_Unique";
            FormManagementModel createdForm = null;

            try
            {
                // Create a form first
                var newForm = new FormManagementModel
                {
                    FormName = testFormName,
                    DisplayName = "Validation Test Form",
                    Category = "Test",
                    IsActive = true
                };

                createdForm = FormManagementRepository.CreateForm(newForm);
                _createdFormIds.Add(createdForm.FormId);

                // Test validation - name should NOT be available for new form
                var isAvailable = FormManagementRepository.IsFormNameAvailable(testFormName);
                Assert.IsFalse(isAvailable, "Form name should not be available (already exists)");

                // Test validation - name should be available for update of same form
                var isAvailableForUpdate = FormManagementRepository.IsFormNameAvailable(testFormName, createdForm.FormId);
                Assert.IsTrue(isAvailableForUpdate, "Form name should be available for updating same form");

                Debug.WriteLine("Form name validation working correctly");
            }
            catch (Exception ex)
            {
                if (createdForm != null)
                    _createdFormIds.Add(createdForm.FormId);
                Assert.Fail($"Form name validation test failed: {ex.Message}");
            }
        }

        #endregion
    }
}
