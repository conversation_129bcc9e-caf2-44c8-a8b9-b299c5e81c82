// FormManagement.cs - Clean, reliable form for managing system forms
// Usage: Provides CRUD operations with DevExpress grid interface

using System;
using System.Collections.Generic;
using System.Data;
using System.Diagnostics;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using DevExpress.XtraGrid;
using DevExpress.XtraGrid.Views.Grid;
using DevExpress.XtraGrid.Columns;
using ProManage.Forms.ReusableForms;
using ProManage.Modules.Data.FormManagement;
using ProManage.Modules.Models.FormManagement;
using ProManage.Modules.Helpers.FormManagement;
using ProManage.Modules.Services;
using ProManage.Modules.UI;
using ProManage.Modules.Connections;

namespace ProManage.Forms.MainForms
{
    /// <summary>
    /// Form Management - Clean, simple form for managing system forms
    /// Provides CRUD operations with reliable DevExpress grid interface
    /// </summary>
    public partial class FormManagement : XtraForm
    {
        #region Properties

        /// <summary>
        /// DataTable for grid binding
        /// </summary>
        private DataTable gridDataTable;

        /// <summary>
        /// MenuRibbon user control for consistent interface
        /// </summary>
        private MenuRibbon menuRibbon;

        /// <summary>
        /// Flag to track if form is in edit mode (adding new row)
        /// </summary>
        private bool isInEditMode = false;

        /// <summary>
        /// Row handle of the new row being added (-1 if none)
        /// </summary>
        private int newRowHandle = -1;

        #endregion

        #region Constructor

        public FormManagement()
        {
            InitializeComponent();

            // Set form properties
            this.Text = "Form Management";
            // Don't set WindowState here - let MDI parent handle it

            // Add event handlers
            this.FormClosed += FormManagement_FormClosed;

            // Initialize components in proper order
            if (!DesignMode)
            {
                InitializeGrid();
                // MenuRibbon and data loading will be handled in Load event
            }
        }

        /// <summary>
        /// Sets the MDI parent for this form
        /// </summary>
        /// <param name="mdiParent">The MDI parent form</param>
        public void SetMdiParent(Form mdiParent)
        {
            this.MdiParent = mdiParent;
        }

        #endregion

        #region Form Events

        private void FormManagement_Load(object sender, EventArgs e)
        {
            try
            {
                Debug.WriteLine("=== FormManagement_Load: Starting ===");

                // Only hide parent ribbon if this is NOT an MDI child
                if (this.MdiParent == null)
                {
                    HideParentRibbon();
                }
                else
                {
                    Debug.WriteLine("Form is MDI child - keeping parent ribbon visible");
                }

                // Clear any existing grid configuration and force reconfiguration
                var gridView = gridControl1.MainView as GridView;
                if (gridView != null)
                {
                    gridView.Columns.Clear();
                    Debug.WriteLine("Cleared existing grid columns");
                }

                // Initialize MenuRibbon only once
                if (menuRibbon == null)
                {
                    InitializeMenuRibbon();
                }

                // Force re-initialization
                InitializeGrid();
                LoadFormData();

                Debug.WriteLine("FormManagement form loaded successfully");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in FormManagement_Load: {ex.Message}");
                MessageBox.Show($"Error loading form: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Handles form closed event to restore parent ribbon
        /// </summary>
        private void FormManagement_FormClosed(object sender, FormClosedEventArgs e)
        {
            try
            {
                // Restore parent ribbon when this form is closed
                ShowParentRibbon();
                Debug.WriteLine("FormManagement form closed, parent ribbon restored");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in FormManagement_FormClosed: {ex.Message}");
            }
        }

        #endregion

        #region Grid Initialization

        /// <summary>
        /// Initializes the grid with proper columns and data binding
        /// </summary>
        private void InitializeGrid()
        {
            try
            {
                Debug.WriteLine("=== InitializeGrid: Starting ===");

                // Create DataTable structure
                CreateGridDataTable();

                // Configure grid control
                ConfigureGridControl();

                // Configure grid view
                ConfigureGridView();

                // Setup grid columns
                SetupGridColumns();

                Debug.WriteLine("Grid initialization completed successfully");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in InitializeGrid: {ex.Message}");
                throw new Exception($"Failed to initialize grid: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Creates the DataTable structure for grid binding
        /// </summary>
        private void CreateGridDataTable()
        {
            gridDataTable = new DataTable("FormManagement");

            // Add checkbox column for row selection
            gridDataTable.Columns.Add("selected", typeof(bool));

            // Add columns matching the database structure
            gridDataTable.Columns.Add("form_id", typeof(int));
            gridDataTable.Columns.Add("form_name", typeof(string));
            gridDataTable.Columns.Add("display_name", typeof(string));
            gridDataTable.Columns.Add("category", typeof(string));
            gridDataTable.Columns.Add("is_active", typeof(bool));
            gridDataTable.Columns.Add("last_modified", typeof(DateTime));

            // Set primary key
            gridDataTable.PrimaryKey = new DataColumn[] { gridDataTable.Columns["form_id"] };

            Debug.WriteLine("DataTable structure created successfully");
        }

        /// <summary>
        /// Configures the grid control properties
        /// </summary>
        private void ConfigureGridControl()
        {
            // Bind DataTable to grid
            gridControl1.DataSource = gridDataTable;
            
            // Basic grid control settings
            gridControl1.UseEmbeddedNavigator = false;
            
            Debug.WriteLine("Grid control configured");
        }

        /// <summary>
        /// Configures the grid view properties
        /// </summary>
        private void ConfigureGridView()
        {
            var gridView = gridControl1.MainView as GridView;
            if (gridView == null) return;

            // Basic view settings
            gridView.OptionsView.ShowGroupPanel = false;
            gridView.OptionsView.ShowAutoFilterRow = true;
            gridView.OptionsView.ShowColumnHeaders = true;
            gridView.OptionsView.ColumnAutoWidth = false;
            
            // Selection settings
            gridView.OptionsSelection.EnableAppearanceFocusedCell = false;
            gridView.OptionsSelection.EnableAppearanceFocusedRow = true;
            gridView.OptionsSelection.MultiSelect = false;

            // Editing settings - allow editing for checkboxes only
            gridView.OptionsBehavior.Editable = true;
            gridView.OptionsBehavior.ReadOnly = false;

            // Appearance settings
            gridView.Appearance.EvenRow.BackColor = Color.White;
            gridView.Appearance.OddRow.BackColor = Color.FromArgb(245, 245, 245);
            gridView.OptionsView.EnableAppearanceEvenRow = true;
            gridView.OptionsView.EnableAppearanceOddRow = true;

            // Setup event handlers
            gridView.FocusedRowChanged += GridView_FocusedRowChanged;
            gridView.DoubleClick += GridView_DoubleClick;
            gridView.ShowingEditor += GridView_ShowingEditor;
            gridView.CellValueChanged += GridView_CellValueChanged;

            Debug.WriteLine("Grid view configured");
        }

        /// <summary>
        /// Sets up grid columns with proper formatting and headers
        /// </summary>
        private void SetupGridColumns()
        {
            var gridView = gridControl1.MainView as GridView;
            if (gridView == null)
            {
                Debug.WriteLine("ERROR: GridView is null!");
                return;
            }

            Debug.WriteLine($"=== SetupGridColumns: Starting - Current columns: {gridView.Columns.Count} ===");

            // FORCE clear any existing columns
            gridView.Columns.Clear();
            Debug.WriteLine("Cleared all existing columns");

            // Checkbox column for selection
            var colSelected = gridView.Columns.AddField("selected");
            colSelected.Caption = "Select";
            colSelected.Width = 60;
            colSelected.VisibleIndex = 0;
            colSelected.OptionsColumn.AllowEdit = true;
            // Remove UnboundType since we have a bound "selected" column in DataTable
            Debug.WriteLine("Added Selection checkbox column");

            // Form ID column (hidden)
            var colFormId = gridView.Columns.AddField("form_id");
            colFormId.Caption = "ID";
            colFormId.Visible = false;
            colFormId.OptionsColumn.AllowEdit = false;
            Debug.WriteLine("Added Form ID column (hidden)");

            // Form Name column
            var colFormName = gridView.Columns.AddField("form_name");
            colFormName.Caption = "Form Name";
            colFormName.Width = 200;
            colFormName.VisibleIndex = 1;
            colFormName.OptionsColumn.AllowEdit = true; // Allow editing for new rows
            Debug.WriteLine("Added Form Name column");

            // Display Name column
            var colDisplayName = gridView.Columns.AddField("display_name");
            colDisplayName.Caption = "Display Name";
            colDisplayName.Width = 250;
            colDisplayName.VisibleIndex = 2;
            colDisplayName.OptionsColumn.AllowEdit = true; // Allow editing for new rows
            Debug.WriteLine("Added Display Name column");

            // Category column
            var colCategory = gridView.Columns.AddField("category");
            colCategory.Caption = "Category";
            colCategory.Width = 150;
            colCategory.VisibleIndex = 3;
            colCategory.OptionsColumn.AllowEdit = true; // Allow editing for new rows
            Debug.WriteLine("Added Category column");

            // Active Status column
            var colIsActive = gridView.Columns.AddField("is_active");
            colIsActive.Caption = "Active";
            colIsActive.Width = 80;
            colIsActive.VisibleIndex = 4;
            colIsActive.OptionsColumn.AllowEdit = true; // Allow editing for new rows
            Debug.WriteLine("Added Active column");

            // Last Modified column
            var colLastModified = gridView.Columns.AddField("last_modified");
            colLastModified.Caption = "Last Modified";
            colLastModified.Width = 150;
            colLastModified.VisibleIndex = 5;
            colLastModified.OptionsColumn.AllowEdit = false;
            colLastModified.DisplayFormat.FormatType = DevExpress.Utils.FormatType.DateTime;
            colLastModified.DisplayFormat.FormatString = "yyyy-MM-dd HH:mm:ss";
            Debug.WriteLine("Added Last Modified column");

            Debug.WriteLine($"=== Grid columns setup completed - Total columns: {gridView.Columns.Count} ===");
        }

        #endregion

        #region Data Loading

        /// <summary>
        /// Loads form data from database
        /// </summary>
        private void LoadFormData()
        {
            try
            {
                Debug.WriteLine("=== LoadFormData: Starting ===");

                // Show progress indicator (safely)
                try
                {
                    ProgressIndicatorService.Instance.ShowProgress();
                }
                catch (Exception progEx)
                {
                    Debug.WriteLine($"Progress indicator error (non-critical): {progEx.Message}");
                }

                // Clear existing data
                gridDataTable.Clear();

                // Get forms from repository
                var forms = FormManagementRepository.GetAllForms();
                Debug.WriteLine($"Retrieved {forms.Count} forms from repository");

                // Populate the grid
                foreach (var form in forms)
                {
                    var row = gridDataTable.NewRow();
                    row["selected"] = false; // Initialize checkbox as unchecked
                    row["form_id"] = form.FormId;
                    row["form_name"] = form.FormName ?? "";
                    row["display_name"] = form.DisplayName ?? "";
                    row["category"] = form.Category ?? "";
                    row["is_active"] = form.IsActive;
                    row["last_modified"] = form.LastModified;
                    gridDataTable.Rows.Add(row);
                }

                // Refresh grid view
                var gridView = gridControl1.MainView as GridView;
                gridView?.RefreshData();

                Debug.WriteLine($"Grid populated with {forms.Count} forms");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in LoadFormData: {ex.Message}");
                MessageBox.Show($"Error loading forms: {ex.Message}\n\nStack trace: {ex.StackTrace}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                // Always hide progress indicator (safely)
                try
                {
                    ProgressIndicatorService.Instance.HideProgress();
                }
                catch (Exception progEx)
                {
                    Debug.WriteLine($"Progress indicator hide error (non-critical): {progEx.Message}");
                }
            }
        }

        /// <summary>
        /// Refreshes the form data from database
        /// </summary>
        public void RefreshData()
        {
            try
            {
                Debug.WriteLine("=== RefreshData called ===");

                // Verify database connection before refreshing
                if (!VerifyDatabaseConnection())
                {
                    Debug.WriteLine("Database verification failed during refresh");
                    return;
                }

                LoadFormData();
                Debug.WriteLine("RefreshData completed successfully");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in RefreshData: {ex.Message}");
                MessageBox.Show($"Error refreshing data: {ex.Message}", "Refresh Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion

        #region Parent Ribbon Management

        /// <summary>
        /// Hides the parent form's ribbon to avoid duplicate ribbons
        /// </summary>
        private void HideParentRibbon()
        {
            try
            {
                if (this.MdiParent != null)
                {
                    // Check if parent is a RibbonForm
                    if (this.MdiParent is DevExpress.XtraBars.Ribbon.RibbonForm ribbonForm)
                    {
                        if (ribbonForm.Ribbon != null)
                        {
                            ribbonForm.Ribbon.Visible = false;
                            Debug.WriteLine("Parent ribbon hidden to avoid duplicate ribbons");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error hiding parent ribbon: {ex.Message}");
            }
        }

        /// <summary>
        /// Shows the parent form's ribbon when this form is closed
        /// </summary>
        private void ShowParentRibbon()
        {
            try
            {
                if (this.MdiParent != null)
                {
                    // Check if parent is a RibbonForm
                    if (this.MdiParent is DevExpress.XtraBars.Ribbon.RibbonForm ribbonForm)
                    {
                        if (ribbonForm.Ribbon != null)
                        {
                            ribbonForm.Ribbon.Visible = true;
                            Debug.WriteLine("Parent ribbon restored");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error showing parent ribbon: {ex.Message}");
            }
        }

        #endregion

        #region Layout Management

        /// <summary>
        /// Adjusts split container size based on MenuRibbon height
        /// </summary>
        private void AdjustSplitContainerSize()
        {
            try
            {
                if (menuRibbon != null)
                {
                    // Get the preferred height of the MenuRibbon
                    int preferredHeight = menuRibbon.PreferredSize.Height;

                    // Ensure minimum height for ribbon
                    if (preferredHeight < 100) preferredHeight = 120;
                    if (preferredHeight > 200) preferredHeight = 150; // Maximum height

                    // Set the split container distance
                    splitContainer1.SplitterDistance = preferredHeight;
                    splitContainer1.Panel1MinSize = preferredHeight;

                    Debug.WriteLine($"Split container adjusted - MenuRibbon height: {preferredHeight}");
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error adjusting split container size: {ex.Message}");
                // Fallback to default size
                splitContainer1.SplitterDistance = 120;
            }
        }

        /// <summary>
        /// Legacy method - now redirects to AdjustSplitContainerSize
        /// </summary>
        private void AdjustGridLayout()
        {
            AdjustSplitContainerSize();
        }

        #endregion

        #region MenuRibbon Integration

        /// <summary>
        /// Initializes the MenuRibbon user control
        /// </summary>
        private void InitializeMenuRibbon()
        {
            try
            {
                // Skip in design mode
                if (DesignMode) return;

                // Prevent double initialization
                if (menuRibbon != null)
                {
                    Debug.WriteLine("MenuRibbon already initialized, skipping...");
                    return;
                }

                Debug.WriteLine("Initializing MenuRibbon for FormManagement...");

                menuRibbon = new MenuRibbon();
                menuRibbon.Dock = DockStyle.Fill;

                // Set form name and user ID for permission checking
                menuRibbon.FormName = "FormManagement";
                menuRibbon.CurrentUserId = 1; // TODO: Get from current session

                // Configure ribbon for Form Management
                menuRibbon.ConfigureForFormType("FormManagement");

                // Clear panel first to avoid duplicates
                panelMenuRibbon.Controls.Clear();

                // Add to the dedicated panel instead of form controls
                panelMenuRibbon.Controls.Add(menuRibbon);

                // Adjust split container size based on MenuRibbon height
                AdjustSplitContainerSize();

                // Setup event handlers
                SetupMenuRibbonEvents();

                Debug.WriteLine("MenuRibbon initialized for FormManagement in dedicated panel");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error initializing MenuRibbon: {ex.Message}");
                Debug.WriteLine($"Stack trace: {ex.StackTrace}");

                // Try to create a fallback ribbon or continue without it
                CreateFallbackMenuRibbon();
            }
        }

        /// <summary>
        /// Creates a fallback menu ribbon when normal initialization fails
        /// </summary>
        private void CreateFallbackMenuRibbon()
        {
            try
            {
                Debug.WriteLine("Creating fallback MenuRibbon...");

                // Clear the panel
                panelMenuRibbon.Controls.Clear();

                // Create a simple panel with basic buttons as fallback
                var fallbackPanel = new Panel();
                fallbackPanel.Dock = DockStyle.Fill;
                fallbackPanel.BackColor = Color.LightGray;
                fallbackPanel.Height = 120;

                // Add basic buttons
                var btnNew = new Button { Text = "New", Size = new Size(60, 30), Location = new Point(10, 10) };
                var btnEdit = new Button { Text = "Edit", Size = new Size(60, 30), Location = new Point(80, 10) };
                var btnSave = new Button { Text = "Save", Size = new Size(60, 30), Location = new Point(150, 10) };
                var btnCancel = new Button { Text = "Cancel", Size = new Size(60, 30), Location = new Point(220, 10) };
                var btnDelete = new Button { Text = "Delete", Size = new Size(60, 30), Location = new Point(290, 10) };
                var btnRefresh = new Button { Text = "Refresh", Size = new Size(60, 30), Location = new Point(360, 10) };

                // Wire up events to the same handlers
                btnNew.Click += (s, e) => MenuRibbon_NewClicked(s, e);
                btnEdit.Click += (s, e) => MenuRibbon_EditClicked(s, e);
                btnSave.Click += (s, e) => MenuRibbon_SaveClicked(s, e);
                btnCancel.Click += (s, e) => MenuRibbon_CancelClicked(s, e);
                btnDelete.Click += (s, e) => MenuRibbon_DeleteClicked(s, e);
                btnRefresh.Click += (s, e) => MenuRibbon_RefreshClicked(s, e);

                fallbackPanel.Controls.AddRange(new Control[] { btnNew, btnEdit, btnSave, btnCancel, btnDelete, btnRefresh });

                panelMenuRibbon.Controls.Add(fallbackPanel);

                Debug.WriteLine("Fallback MenuRibbon created successfully");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Even fallback MenuRibbon creation failed: {ex.Message}");

                // Last resort - just add a label
                try
                {
                    panelMenuRibbon.Controls.Clear();
                    var lblError = new Label();
                    lblError.Text = "Menu ribbon unavailable - using grid double-click for editing";
                    lblError.Dock = DockStyle.Fill;
                    lblError.TextAlign = ContentAlignment.MiddleCenter;
                    lblError.BackColor = Color.LightYellow;
                    panelMenuRibbon.Controls.Add(lblError);
                }
                catch
                {
                    // Give up on ribbon entirely
                }
            }
        }

        /// <summary>
        /// Sets up event handlers for MenuRibbon buttons
        /// </summary>
        private void SetupMenuRibbonEvents()
        {
            if (menuRibbon != null)
            {
                // New button event handler
                menuRibbon.NewClicked += MenuRibbon_NewClicked;

                // Edit button event handler
                menuRibbon.EditClicked += MenuRibbon_EditClicked;

                // Save button event handler
                menuRibbon.SaveClicked += MenuRibbon_SaveClicked;

                // Cancel button event handler
                menuRibbon.CancelClicked += MenuRibbon_CancelClicked;

                // Delete button event handler
                menuRibbon.DeleteClicked += MenuRibbon_DeleteClicked;

                // Use Print button as Refresh for now
                menuRibbon.PrintClicked += MenuRibbon_RefreshClicked;
                menuRibbon.SetButtonText("Print", "Refresh");
            }
        }

        #endregion

        #region Event Handlers

        /// <summary>
        /// Handles grid row selection changes
        /// </summary>
        private void GridView_FocusedRowChanged(object sender, DevExpress.XtraGrid.Views.Base.FocusedRowChangedEventArgs e)
        {
            try
            {
                // No need to update button states since they're always enabled
                Debug.WriteLine($"Focused row changed to: {e.FocusedRowHandle}");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in GridView_FocusedRowChanged: {ex.Message}");
            }
        }

        /// <summary>
        /// Handles grid double-click for editing
        /// </summary>
        private void GridView_DoubleClick(object sender, EventArgs e)
        {
            try
            {
                // Disable double-click editing when in edit mode
                if (isInEditMode) return;

                var selectedForms = GetSelectedForms();
                if (selectedForms.Count == 1)
                {
                    EditSelectedForm(selectedForms[0]);
                }
                else if (selectedForms.Count == 0)
                {
                    MessageBox.Show("Please select a form to edit.", "No Selection",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                else
                {
                    MessageBox.Show("Please select only one form to edit.", "Multiple Selection",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in GridView_DoubleClick: {ex.Message}");
                MessageBox.Show($"Error opening form for editing: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }



        /// <summary>
        /// Controls which cells can be edited
        /// </summary>
        private void GridView_ShowingEditor(object sender, System.ComponentModel.CancelEventArgs e)
        {
            try
            {
                var gridView = sender as GridView;
                if (gridView == null)
                {
                    e.Cancel = true;
                    return;
                }

                string fieldName = gridView.FocusedColumn?.FieldName;
                int rowHandle = gridView.FocusedRowHandle;

                Debug.WriteLine($"ShowingEditor: Field={fieldName}, Row={rowHandle}, EditMode={isInEditMode}, NewRowHandle={newRowHandle}");

                // Always allow editing checkboxes (regardless of edit mode)
                if (fieldName == "selected")
                {
                    Debug.WriteLine("Allowing checkbox editing");
                    e.Cancel = false;
                    return;
                }

                // Allow editing new row fields when in edit mode (except form_id which is auto-generated)
                if (isInEditMode && rowHandle == newRowHandle && fieldName != "form_id")
                {
                    Debug.WriteLine($"Allowing new row field editing: {fieldName}");
                    e.Cancel = false;
                    return;
                }

                // Cancel all other editing
                Debug.WriteLine($"Canceling editing for field: {fieldName}");
                e.Cancel = true;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in GridView_ShowingEditor: {ex.Message}");
                e.Cancel = true;
            }
        }

        /// <summary>
        /// Handles cell value changes to ensure checkbox changes are committed
        /// </summary>
        private void GridView_CellValueChanged(object sender, DevExpress.XtraGrid.Views.Base.CellValueChangedEventArgs e)
        {
            try
            {
                var gridView = sender as GridView;
                if (gridView == null) return;

                // If checkbox value changed, ensure it's committed to the DataTable
                if (e.Column?.FieldName == "selected")
                {
                    Debug.WriteLine($"Checkbox changed for row {e.RowHandle}: {e.Value}");

                    // Force update the current row to commit changes
                    gridView.UpdateCurrentRow();

                    // Update the underlying DataTable directly to ensure the change is persisted
                    var row = gridView.GetDataRow(e.RowHandle);
                    if (row != null)
                    {
                        row["selected"] = e.Value ?? false;
                        Debug.WriteLine($"Updated DataTable row {e.RowHandle} selected = {row["selected"]}");
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in GridView_CellValueChanged: {ex.Message}");
            }
        }

        /// <summary>
        /// Handles New button click - adds new row to grid for editing
        /// </summary>
        private void MenuRibbon_NewClicked(object sender, EventArgs e)
        {
            try
            {
                if (isInEditMode)
                {
                    MessageBox.Show("Please save or cancel the current edit before adding a new form.", "Edit in Progress",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                // Enter edit mode
                isInEditMode = true;

                // Add new row to the grid
                var newRow = gridDataTable.NewRow();
                newRow["selected"] = false;
                newRow["form_id"] = 0; // Will be set by database
                newRow["form_name"] = "";
                newRow["display_name"] = "";
                newRow["category"] = "";
                newRow["is_active"] = true;
                newRow["last_modified"] = DateTime.Now;
                gridDataTable.Rows.Add(newRow);

                // Get the row handle of the new row
                var gridView = gridControl1.MainView as GridView;
                if (gridView != null)
                {
                    newRowHandle = gridView.GetRowHandle(gridDataTable.Rows.Count - 1);
                    gridView.FocusedRowHandle = newRowHandle;
                    gridView.FocusedColumn = gridView.Columns["form_name"];

                    Debug.WriteLine($"New row added - DataTable rows: {gridDataTable.Rows.Count}, Row handle: {newRowHandle}");
                    Debug.WriteLine($"Focused row handle: {gridView.FocusedRowHandle}");
                    Debug.WriteLine($"Focused column: {gridView.FocusedColumn?.FieldName}");
                }

                // Update button states
                UpdateButtonStates();

                Debug.WriteLine("New row added for editing - process complete");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in New button click: {ex.Message}");
                MessageBox.Show($"Error adding new form: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);

                // Reset edit mode on error
                isInEditMode = false;
                newRowHandle = -1;
                UpdateButtonStates();
            }
        }

        /// <summary>
        /// Handles Save button click - saves the new or edited row to database
        /// </summary>
        private void MenuRibbon_SaveClicked(object sender, EventArgs e)
        {
            try
            {
                if (!isInEditMode || newRowHandle == -1)
                {
                    MessageBox.Show("No changes to save.", "No Edit in Progress",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                var gridView = gridControl1.MainView as GridView;
                if (gridView == null) return;

                // Get the row data
                var row = gridView.GetDataRow(newRowHandle);
                if (row == null) return;

                // Validate required fields
                string formName = row["form_name"]?.ToString()?.Trim();
                if (string.IsNullOrEmpty(formName))
                {
                    MessageBox.Show("Form Name is required.", "Validation Error",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                // ENHANCED: Check database connection before attempting save
                if (!VerifyDatabaseConnection())
                {
                    return; // Error message already shown in VerifyDatabaseConnection
                }

                // Determine if this is a new form (form_id = 0) or existing form (form_id > 0)
                var formIdValue = row["form_id"];
                int formId = formIdValue != null && formIdValue != DBNull.Value ? Convert.ToInt32(formIdValue) : 0;
                bool isNewForm = formId == 0;

                // Create form model
                var formModel = new FormManagementModel
                {
                    FormId = formId,
                    FormName = formName,
                    DisplayName = row["display_name"]?.ToString()?.Trim() ?? "",
                    Category = row["category"]?.ToString()?.Trim() ?? "",
                    IsActive = Convert.ToBoolean(row["is_active"])
                };

                // Show progress indicator
                ProgressIndicatorService.Instance.ShowProgress();

                try
                {
                    FormManagementModel savedForm = null;
                    
                    if (isNewForm)
                    {
                        // Create new form
                        savedForm = FormManagementRepository.CreateForm(formModel);
                        Debug.WriteLine($"Created new form with ID: {savedForm?.FormId}");
                    }
                    else
                    {
                        // Update existing form
                        savedForm = FormManagementRepository.UpdateForm(formModel);
                        Debug.WriteLine($"Updated existing form ID: {formModel.FormId}");
                    }
                    
                    if (savedForm != null)
                    {
                        // Exit edit mode
                        isInEditMode = false;
                        newRowHandle = -1;

                        // Refresh data to show the saved record with proper ID
                        RefreshData();
                        UpdateButtonStates();

                        string successMessage = isNewForm ? "Form created successfully!" : "Form updated successfully!";
                        MessageBox.Show(successMessage, "Success",
                            MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                    else
                    {
                        string errorMessage = isNewForm ? "Failed to create the form. Please try again." : "Failed to update the form. Please try again.";
                        MessageBox.Show(errorMessage, "Save Error",
                            MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
                finally
                {
                    ProgressIndicatorService.Instance.HideProgress();
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in Save button click: {ex.Message}");
                Debug.WriteLine($"Stack trace: {ex.StackTrace}");

                // Enhanced error message with more details
                string errorMessage = $"Error saving form: {ex.Message}";
                if (ex.InnerException != null)
                {
                    errorMessage += $"\n\nDetails: {ex.InnerException.Message}";
                }

                MessageBox.Show(errorMessage, "Database Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Handles Cancel button click - cancels the new row editing
        /// </summary>
        private void MenuRibbon_CancelClicked(object sender, EventArgs e)
        {
            try
            {
                if (!isInEditMode || newRowHandle == -1)
                {
                    MessageBox.Show("No edit operation to cancel.", "No Edit in Progress",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                var result = MessageBox.Show("Are you sure you want to cancel? All unsaved changes will be lost.",
                    "Confirm Cancel", MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    // Remove the new row
                    var gridView = gridControl1.MainView as GridView;
                    if (gridView != null)
                    {
                        var row = gridView.GetDataRow(newRowHandle);
                        if (row != null)
                        {
                            gridDataTable.Rows.Remove(row);
                        }
                    }

                    // Exit edit mode
                    isInEditMode = false;
                    newRowHandle = -1;

                    // Update button states
                    UpdateButtonStates();

                    Debug.WriteLine("New row editing cancelled");
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in Cancel button click: {ex.Message}");
                MessageBox.Show($"Error cancelling edit: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Handles Edit button click
        /// </summary>
        private void MenuRibbon_EditClicked(object sender, EventArgs e)
        {
            try
            {
                Debug.WriteLine("=== Edit button clicked ===");

                if (isInEditMode)
                {
                    MessageBox.Show("Please save or cancel the current edit before editing another form.", "Edit in Progress",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                var selectedForms = GetSelectedForms();
                Debug.WriteLine($"Selected forms count: {selectedForms.Count}");

                if (selectedForms.Count == 1)
                {
                    Debug.WriteLine($"Editing form: {selectedForms[0].FormName}");
                    EditSelectedForm(selectedForms[0]);
                }
                else if (selectedForms.Count == 0)
                {
                    MessageBox.Show("Please select a form to edit by checking the checkbox.", "No Selection",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                else
                {
                    MessageBox.Show("Please select only one form to edit.", "Multiple Selection",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in Edit button click: {ex.Message}");
                Debug.WriteLine($"Stack trace: {ex.StackTrace}");
                MessageBox.Show($"Error editing form: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Handles Delete button click
        /// </summary>
        private void MenuRibbon_DeleteClicked(object sender, EventArgs e)
        {
            try
            {
                Debug.WriteLine("=== Delete button clicked ===");

                // DEBUGGING: First verify table structure
                Debug.WriteLine("Verifying forms table structure...");
                string tableVerification = FormManagementRepository.VerifyFormsTableStructure();
                Debug.WriteLine($"Table verification result: {tableVerification}");

                if (tableVerification.StartsWith("ERROR"))
                {
                    MessageBox.Show($"Database structure issue detected:\n\n{tableVerification}\n\nPlease check your database connection and table structure.",
                        "Database Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }

                if (isInEditMode)
                {
                    MessageBox.Show("Please save or cancel the current edit before deleting forms.", "Edit in Progress",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                var selectedForms = GetSelectedForms();
                Debug.WriteLine($"Selected forms count for deletion: {selectedForms.Count}");

                if (selectedForms.Count == 0)
                {
                    MessageBox.Show("Please select one or more forms to delete by checking the checkboxes.", "No Selection",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                // DEBUGGING: Test the first selected form before actual deletion
                if (selectedForms.Count > 0)
                {
                    var firstForm = selectedForms[0];
                    Debug.WriteLine($"Testing deletion for form ID: {firstForm.FormId}, Name: {firstForm.FormName}");

                    string testResult = FormManagementRepository.TestDeleteForm(firstForm.FormId);
                    Debug.WriteLine($"Test deletion result: {testResult}");

                    if (testResult.StartsWith("ERROR") || testResult.StartsWith("EXCEPTION"))
                    {
                        MessageBox.Show($"Cannot delete form - database issue detected:\n\n{testResult}\n\nForm ID: {firstForm.FormId}\nForm Name: {firstForm.FormName}",
                            "Database Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                        return;
                    }
                }

                string message = selectedForms.Count == 1
                    ? $"Are you sure you want to delete the form '{selectedForms[0].FormName}'?"
                    : $"Are you sure you want to delete {selectedForms.Count} selected forms?";

                var result = MessageBox.Show(message, "Confirm Delete",
                    MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    int deletedCount = 0;
                    var errors = new List<string>();

                    foreach (var form in selectedForms)
                    {
                        try
                        {
                            if (FormManagementHelper.DeleteForm(form.FormId, form.FormName))
                            {
                                deletedCount++;
                            }
                            else
                            {
                                errors.Add($"Failed to delete '{form.FormName}'");
                            }
                        }
                        catch (Exception deleteEx)
                        {
                            errors.Add($"Error deleting '{form.FormName}': {deleteEx.Message}");
                        }
                    }

                    // Refresh data
                    RefreshData();
                    UpdateButtonStates();

                    // Show results
                    if (errors.Count == 0)
                    {
                        string successMessage = deletedCount == 1
                            ? "Form deleted successfully!"
                            : $"{deletedCount} forms deleted successfully!";
                        MessageBox.Show(successMessage, "Success",
                            MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                    else
                    {
                        string errorMessage = $"{deletedCount} forms deleted successfully.\n\nErrors:\n" +
                                            string.Join("\n", errors);
                        MessageBox.Show(errorMessage, "Partial Success",
                            MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in Delete button click: {ex.Message}");
                MessageBox.Show($"Error deleting forms: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Handles Refresh button click
        /// </summary>
        private void MenuRibbon_RefreshClicked(object sender, EventArgs e)
        {
            try
            {
                // Show progress indicator
                ProgressIndicatorService.Instance.ShowProgress();

                RefreshData();
                MessageBox.Show("Data refreshed successfully!", "Refresh Complete",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error refreshing data: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                ProgressIndicatorService.Instance.HideProgress();
            }
        }

        /// <summary>
        /// Diagnostic method to test database connection and schema
        /// Call this method if you're experiencing database issues
        /// </summary>
        public void DiagnoseDatabaseIssues()
        {
            try
            {
                Debug.WriteLine("=== DATABASE DIAGNOSTIC STARTED ===");

                var diagnosticResults = new System.Text.StringBuilder();
                diagnosticResults.AppendLine("Database Diagnostic Results:");
                diagnosticResults.AppendLine("============================");

                // Test 1: Check if database is configured
                bool isConfigured = DatabaseConnectionManager.Instance.IsConfigured;
                diagnosticResults.AppendLine($"1. Database Configured: {isConfigured}");

                if (!isConfigured)
                {
                    diagnosticResults.AppendLine("   → Database is not configured. Please configure database connection first.");
                }

                // Test 2: Check if database is connected
                bool isConnected = DatabaseConnectionManager.Instance.IsConnected;
                diagnosticResults.AppendLine($"2. Database Connected: {isConnected}");

                if (!isConnected && isConfigured)
                {
                    diagnosticResults.AppendLine("   → Attempting to connect...");
                    bool connectionResult = DatabaseConnectionManager.Instance.OpenConnection();
                    diagnosticResults.AppendLine($"   → Connection attempt result: {connectionResult}");

                    if (!connectionResult)
                    {
                        string error = DatabaseConnectionManager.Instance.LastError ?? "Unknown error";
                        diagnosticResults.AppendLine($"   → Connection error: {error}");
                    }
                }

                // Test 3: Check if forms table exists
                bool tableExists = VerifyFormsTableExists();
                diagnosticResults.AppendLine($"3. Forms Table Exists: {tableExists}");

                if (!tableExists)
                {
                    diagnosticResults.AppendLine("   → Forms table is missing. Attempting to create...");
                    bool createResult = CreateFormsTable();
                    diagnosticResults.AppendLine($"   → Table creation result: {createResult}");
                }

                // Test 4: Try to execute a simple query
                try
                {
                    var testResult = QueryExecutor.ExecuteSelectQuery("SELECT COUNT(*) FROM forms", out string queryError);
                    if (string.IsNullOrEmpty(queryError) && testResult != null)
                    {
                        int recordCount = Convert.ToInt32(testResult.Rows[0][0]);
                        diagnosticResults.AppendLine($"4. Query Test: SUCCESS (Found {recordCount} records)");
                    }
                    else
                    {
                        diagnosticResults.AppendLine($"4. Query Test: FAILED - {queryError}");
                    }
                }
                catch (Exception queryEx)
                {
                    diagnosticResults.AppendLine($"4. Query Test: FAILED - {queryEx.Message}");
                }

                diagnosticResults.AppendLine("============================");

                Debug.WriteLine(diagnosticResults.ToString());

                // Show results to user
                MessageBox.Show(diagnosticResults.ToString(), "Database Diagnostic Results",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);

                Debug.WriteLine("=== DATABASE DIAGNOSTIC COMPLETED ===");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in database diagnostic: {ex.Message}");
                MessageBox.Show($"Error running database diagnostic: {ex.Message}", "Diagnostic Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion

        #region Helper Methods

        /// <summary>
        /// Gets all selected forms from the grid based on checkboxes
        /// </summary>
        /// <returns>List of selected FormManagementModel objects</returns>
        private List<FormManagementModel> GetSelectedForms()
        {
            var selectedForms = new List<FormManagementModel>();

            try
            {
                var gridView = gridControl1.MainView as GridView;
                if (gridView == null)
                {
                    Debug.WriteLine("GetSelectedForms: GridView is null");
                    return selectedForms;
                }

                // CRITICAL FIX: Force commit any pending edits to ensure checkbox changes are saved to DataTable
                gridView.PostEditor();
                gridView.UpdateCurrentRow();

                Debug.WriteLine($"GetSelectedForms: Checking {gridView.RowCount} rows for selection");

                for (int i = 0; i < gridView.RowCount; i++)
                {
                    var row = gridView.GetDataRow(i);
                    if (row != null)
                    {
                        // Get checkbox value with additional debugging
                        var selectedValue = row["selected"];
                        bool isSelected = Convert.ToBoolean(selectedValue);

                        Debug.WriteLine($"Row {i}: selected value = '{selectedValue}', converted = {isSelected}");

                        if (isSelected)
                        {
                            var form = FormManagementModel.FromDataRow(row);
                            if (form != null)
                            {
                                selectedForms.Add(form);
                                Debug.WriteLine($"Added selected form: {form.FormName}");
                            }
                            else
                            {
                                Debug.WriteLine($"WARNING: Could not create FormManagementModel from row {i}");
                            }
                        }
                    }
                    else
                    {
                        Debug.WriteLine($"WARNING: Row {i} is null");
                    }
                }

                Debug.WriteLine($"GetSelectedForms: Found {selectedForms.Count} selected forms");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in GetSelectedForms: {ex.Message}");
                Debug.WriteLine($"Stack trace: {ex.StackTrace}");
            }

            return selectedForms;
        }

        /// <summary>
        /// Gets the currently selected form from the grid (legacy method for compatibility)
        /// </summary>
        /// <returns>Selected FormManagementModel or null</returns>
        private FormManagementModel GetSelectedForm()
        {
            try
            {
                var selectedForms = GetSelectedForms();
                return selectedForms.FirstOrDefault();
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in GetSelectedForm: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// Updates button states using the standard MenuRibbon methods
        /// For FormManagement: Keep Edit/Delete always enabled, functionality controlled in click handlers
        /// </summary>
        private void UpdateButtonStates()
        {
            try
            {
                Debug.WriteLine($"=== UpdateButtonStates ===");
                Debug.WriteLine($"Edit mode: {isInEditMode}");

                // Update MenuRibbon button states if available
                if (menuRibbon != null)
                {
                    // Use the standard MenuRibbon button control methods
                    // Keep Edit and Delete always enabled when not in edit mode
                    if (!isInEditMode)
                    {
                        menuRibbon.SetButtonEnabled("Edit", true);
                        menuRibbon.SetButtonEnabled("Delete", true);
                    }
                    else
                    {
                        // In edit mode, disable Edit and Delete
                        menuRibbon.SetButtonEnabled("Edit", false);
                        menuRibbon.SetButtonEnabled("Delete", false);
                    }

                    // Save and Cancel based on edit mode
                    menuRibbon.SetButtonEnabled("Save", isInEditMode);
                    menuRibbon.SetButtonEnabled("Cancel", isInEditMode);

                    Debug.WriteLine($"MenuRibbon button states updated successfully");
                }
                else
                {
                    Debug.WriteLine("WARNING: MenuRibbon is null, cannot update button states");
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in UpdateButtonStates: {ex.Message}");
                Debug.WriteLine($"Stack trace: {ex.StackTrace}");
            }
        }

        /// <summary>
        /// Opens the selected form for editing using inline editing
        /// </summary>
        /// <param name="formToEdit">Form to edit</param>
        private void EditSelectedForm(FormManagementModel formToEdit)
        {
            try
            {
                Debug.WriteLine($"=== EditSelectedForm: {formToEdit.FormName} ===");
                
                var gridView = gridControl1.MainView as GridView;
                if (gridView == null)
                {
                    MessageBox.Show("Grid view is not available for editing.", "Error", 
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }
                
                // Find the row for this form in the grid
                for (int i = 0; i < gridView.RowCount; i++)
                {
                    var rowFormId = Convert.ToInt32(gridView.GetRowCellValue(i, "form_id"));
                    if (rowFormId == formToEdit.FormId)
                    {
                        // Enable edit mode
                        isInEditMode = true;
                        newRowHandle = i; // Use the existing row handle for editing
                        
                        // Focus on the row to be edited
                        gridView.FocusedRowHandle = i;
                        
                        // Update button states to show Save/Cancel
                        UpdateButtonStates();
                        
                        Debug.WriteLine($"Enabled inline editing for form ID {formToEdit.FormId} at row {i}");
                        MessageBox.Show($"You can now edit the form '{formToEdit.FormName}'. Click Save when finished or Cancel to discard changes.", 
                            "Edit Mode", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        break;
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in EditSelectedForm: {ex.Message}");
                MessageBox.Show($"Error enabling edit mode: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Verifies database connection and schema before attempting database operations
        /// </summary>
        /// <returns>True if database is ready, false otherwise</returns>
        private bool VerifyDatabaseConnection()
        {
            try
            {
                Debug.WriteLine("=== Verifying database connection ===");

                // Check if database connection manager is configured
                if (!DatabaseConnectionManager.Instance.IsConfigured)
                {
                    MessageBox.Show(
                        "Database is not configured. Please configure the database connection first.\n\n" +
                        "Go to Database Configuration to set up your connection.",
                        "Database Not Configured",
                        MessageBoxButtons.OK,
                        MessageBoxIcon.Warning);
                    return false;
                }

                // Check if database is connected
                if (!DatabaseConnectionManager.Instance.IsConnected)
                {
                    Debug.WriteLine("Database not connected, attempting to connect...");

                    if (!DatabaseConnectionManager.Instance.OpenConnection())
                    {
                        string error = DatabaseConnectionManager.Instance.LastError ?? "Unknown connection error";
                        MessageBox.Show(
                            $"Cannot connect to the database. Please check your connection settings.\n\n" +
                            $"Error: {error}",
                            "Database Connection Failed",
                            MessageBoxButtons.OK,
                            MessageBoxIcon.Error);
                        return false;
                    }
                }

                // Verify forms table exists
                if (!VerifyFormsTableExists())
                {
                    Debug.WriteLine("Forms table does not exist, attempting to create...");

                    if (!CreateFormsTable())
                    {
                        MessageBox.Show(
                            "The forms table does not exist and could not be created.\n\n" +
                            "Please check your database permissions or contact your administrator.",
                            "Database Schema Error",
                            MessageBoxButtons.OK,
                            MessageBoxIcon.Error);
                        return false;
                    }
                }

                Debug.WriteLine("Database verification successful");
                return true;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error verifying database connection: {ex.Message}");
                MessageBox.Show(
                    $"Error verifying database connection: {ex.Message}",
                    "Database Verification Error",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Error);
                return false;
            }
        }

        /// <summary>
        /// Checks if the forms table exists in the database
        /// </summary>
        /// <returns>True if table exists, false otherwise</returns>
        private bool VerifyFormsTableExists()
        {
            try
            {
                string checkTableQuery = @"
                    SELECT EXISTS (
                        SELECT FROM information_schema.tables
                        WHERE table_schema = 'public'
                        AND table_name = 'forms'
                    );";

                var result = QueryExecutor.ExecuteSelectQuery(checkTableQuery, out string errorMessage);

                if (!string.IsNullOrEmpty(errorMessage))
                {
                    Debug.WriteLine($"Error checking forms table existence: {errorMessage}");
                    return false;
                }

                if (result != null && result.Rows.Count > 0)
                {
                    bool exists = Convert.ToBoolean(result.Rows[0][0]);
                    Debug.WriteLine($"Forms table exists: {exists}");
                    return exists;
                }

                return false;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error verifying forms table: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Creates the forms table using the schema initializer
        /// </summary>
        /// <returns>True if table created successfully, false otherwise</returns>
        private bool CreateFormsTable()
        {
            try
            {
                Debug.WriteLine("Attempting to create forms table...");

                // Use the existing schema initializer
                bool result = DatabaseSchemaInitializer.InitializeDatabase();

                if (result)
                {
                    Debug.WriteLine("Forms table created successfully");

                    // Verify the table was actually created
                    return VerifyFormsTableExists();
                }
                else
                {
                    Debug.WriteLine("Failed to create forms table via schema initializer");
                    return false;
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error creating forms table: {ex.Message}");
                return false;
            }
        }

        #endregion
    }
}