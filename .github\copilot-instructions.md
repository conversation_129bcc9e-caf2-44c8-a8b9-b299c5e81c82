# Below are the Rules you must strictly follow to ensure consistency and quality in the project.

# Always create a detailed plan how you will handle / solve the task.

## Critical Rules
1. **Component Replacement**: Always confirm with user before replacing any UI components.
2. **Design Updates**: Clarify if changes should be made via code or as permanent design changes.
3. **SQL Location**: All SQL queries must be in Procedures folder only; no SQL in code files.
4. **Project Understanding**: If stuck or unclear about project concept, refer to ProjectIdea.md file before proceeding.
5. **Progress Indicator**: Use ProgressIndicatorService for database operations to provide visual feedback.
6. **Parameters**: Use ParameterService.GetParameter<T>() instead of hardcoded values throughout the application.
7. **File Splitting**: Target 500 lines per file; allow up to 800 lines if splitting creates excessive fragmentation.
8. **File Naming**: File names should start with form name together with functionality.
9. **Tests**: Always create tests for new features or new forms in the Tests folder which can be run with dotnet test command. all files related to test should be in the same folder but in sub folder.
10. **Error Handling**: Whenever user submit any error focus on the error donot add or remove any UI elements without user permission, also donot add or delete any file without user permission.

## File Organization in a Modular Structure
The project follows a modular structure to ensure clarity, maintainability, and scalability. Each module is self-contained and organized into specific folders based on functionality. This structure promotes separation of concerns and simplifies navigation within the project.
## Module Organization
- **Data**: Database connections, repositories, transaction services
- **EventHandlers**: Form event handlers (separate from UI logic)
- **Helpers**: Utility classes (stateless when possible)
- **Models**: Data models (plain classes with minimal logic)
- **Procedures**: SQL query files (organized by module)
- **UI**: UI managers, custom controls, UI utilities
- **Validation**: Input validators, business rule validators

## SQL Management
- Store SQL files in `Modules/Procedures/{ModuleName}/` folders only
- Name files by purpose: `Get[Entity].sql`, `Update[Entity].sql`, `Insert[Entity].sql`, `Delete[Entity].sql`
- Load queries with `SQLQueryLoader.LoadQuery("ModuleName", "QueryName")`
- Execute with `QueryExecutor.ExecuteQueryFromFile()` for consistency and security
- Use parameterized queries with `@paramName` format to prevent SQL injection
- Never concatenate user input into SQL strings or embed SQL in code files

## UI Components
### DevExpress (Primary Framework)
- **Main Components**: XtraTabbedMdiManager, AccordionControl, RibbonControl, GridControl
- **Known Limitations**:
  - AccordionControl: No TextOrientation property, ReadOnly ImageOptions
  - VGridControl: Missing support for various properties
  - GridView: Missing navigation properties
  - MarqueeProgressBarControl: Cannot cast to ISupportInitialize

### Syncfusion (Complementary)
- **Main Components**: SfDataGrid, SfPdfViewer, SfChart, SfTreeView
- **Known Limitations**:
  - No toolbox/designer support for .NET Core projects
  - Style inconsistencies with DevExpress
  - Performance differences with large datasets

### When to Use Each:
- **DevExpress**: Main application shell, standard forms, ribbon navigation, MDI architecture
- **Syncfusion**: When DevExpress has limitations, for PDF viewing, advanced charting

### Licensing:
- **DevExpress**: Per-developer model, permanent licenses
- **Syncfusion**: Community License (free for small businesses), register with `SyncfusionLicenseProvider.RegisterLicense()`

## Form Design Standards
- Black borders on all controls
- Light gray backgrounds (Color.FromArgb(245, 245, 245))
- Consistent font (Segoe UI, 9pt)
- Row-wise data entry (Excel-like)
- Immediate validation with clear error messages

## Namespace Conventions
```csharp
// Root namespace
namespace ProManage_8
{
    // Code here
}

// Forms namespace
namespace ProManage_8.Forms
{
    // Code here
}

// Modules namespace
namespace ProManage_8.Modules.SubfolderName
{
    // Code here
}
```

- Always use full namespace paths in using statements
- Update both declarations and using statements when moving files

## Coding Standards

### Naming
- **PascalCase**: Classes, properties, methods, namespaces
- **camelCase**: Local variables, parameters
- **_camelCase**: Private fields
- **ALL_CAPS**: Constants
- **Prefixes**: txt, cbo, chk, btn, dtp, grid for controls

### Documentation
```csharp
/// <summary>
/// Description of class/method
/// </summary>
/// <param name="paramName">Parameter description</param>
/// <returns>Return value description</returns>
```

### Error Handling
- Use structured try/catch/finally
- Catch specific exceptions before general ones
- Log exceptions with appropriate detail
- Clean up resources in finally blocks or use using statements

### Function Signatures
- Use descriptive parameter names
- Use appropriate parameter types
- Use optional parameters or method overloads for optional values
- Use out or ref parameters for output parameters

### Security
- No sensitive information in plain text
- Use parameterized queries
- Validate all user input
- Implement proper authentication

## Progress Indicator Implementation
- Use singleton `ProgressIndicatorService` to control MainFrame's progress bar
- Show progress bar at start of database operations with `ShowProgress()`
- Hide progress bar in `finally` blocks with `HideProgress()`
- Minimum display time of 500ms ensures visibility to users
- Avoid redundant database calls that hide/show progress bar too quickly
- For nested operations, only hide progress bar in outermost operation

## Parameters System
- **Centralized Service**: Use `ParameterService.GetParameter<T>("key")` to replace all hardcoded values
- **Type Safety**: Support string, number, boolean, date types with memory caching at startup
- **Self-Healing**: System auto-creates missing parameters with defaults
- **Parameter Forms**: Use TextEdit (string), SpinEdit (number), CheckEdit (boolean), DateEdit (date)
- **Key Naming**: Use hierarchical format like "UI.Grid.DefaultPageSize"
- **No Hardcoding**: Convert all configurable values to parameter lookups
