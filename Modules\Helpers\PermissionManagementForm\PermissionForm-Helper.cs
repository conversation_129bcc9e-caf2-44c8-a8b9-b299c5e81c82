using System;
using ProManage.Modules.Models.PermissionManagementForm;

namespace ProManage.Modules.Helpers.PermissionManagementForm
{
    /// <summary>
    /// Helper class for permission-related business logic
    /// Contains utility methods moved from model classes to maintain clean separation
    /// </summary>
    public static class PermissionFormHelper
    {
        #region Role Helper Methods

        /// <summary>
        /// Updates the modify date of a role to current time when editing
        /// Note: This should only be called when the user saves changes, not on creation
        /// </summary>
        /// <param name="role">Role to update</param>
        public static void UpdateRoleModifyDate(Role role)
        {
            if (role != null)
            {
                role.ModifyDate = DateTime.Now;
            }
        }

        /// <summary>
        /// Creates a copy of the role for editing
        /// </summary>
        /// <param name="role">Role to clone</param>
        /// <returns>Copy of the role</returns>
        public static Role CloneRole(Role role)
        {
            if (role == null)
                return null;

            return new Role
            {
                RoleId = role.RoleId,
                RoleName = role.RoleName,
                Description = role.Description,
                IsActive = role.IsActive,
                CreatedDate = role.CreatedDate,
                ModifyDate = role.ModifyDate
            };
        }

        #endregion

        #region User Permission Helper Methods

        /// <summary>
        /// Checks if any permission is set (not all NULL) in user permission update
        /// </summary>
        /// <param name="update">User permission update to check</param>
        /// <returns>True if at least one permission is set, false if all are NULL</returns>
        public static bool HasAnyPermissionSet(UserPermissionUpdate update)
        {
            if (update == null)
                return false;

            return update.ReadPermission.HasValue ||
                   update.NewPermission.HasValue ||
                   update.EditPermission.HasValue ||
                   update.DeletePermission.HasValue ||
                   update.PrintPermission.HasValue;
        }

        /// <summary>
        /// Resets all permissions to NULL (inherit from role) in user permission update
        /// </summary>
        /// <param name="update">User permission update to reset</param>
        public static void ResetToRoleInheritance(UserPermissionUpdate update)
        {
            if (update != null)
            {
                update.ReadPermission = null;
                update.NewPermission = null;
                update.EditPermission = null;
                update.DeletePermission = null;
                update.PrintPermission = null;
            }
        }

        /// <summary>
        /// Checks if any global permission is granted
        /// </summary>
        /// <param name="update">Global permission update to check</param>
        /// <returns>True if at least one global permission is true</returns>
        public static bool HasAnyGlobalPermission(GlobalPermissionUpdate update)
        {
            if (update == null)
                return false;

            return update.CanCreateUsers || update.CanEditUsers || 
                   update.CanDeleteUsers || update.CanPrintUsers;
        }

        /// <summary>
        /// Grants all global permissions
        /// </summary>
        /// <param name="update">Global permission update to modify</param>
        public static void GrantAllPermissions(GlobalPermissionUpdate update)
        {
            if (update != null)
            {
                update.CanCreateUsers = true;
                update.CanEditUsers = true;
                update.CanDeleteUsers = true;
                update.CanPrintUsers = true;
            }
        }

        /// <summary>
        /// Revokes all global permissions
        /// </summary>
        /// <param name="update">Global permission update to modify</param>
        public static void RevokeAllPermissions(GlobalPermissionUpdate update)
        {
            if (update != null)
            {
                update.CanCreateUsers = false;
                update.CanEditUsers = false;
                update.CanDeleteUsers = false;
                update.CanPrintUsers = false;
            }
        }

        #endregion

        #region Permission Analysis Helper Methods

        /// <summary>
        /// Determines if a user has effective permission based on role and user overrides
        /// </summary>
        /// <param name="rolePermission">Permission from role</param>
        /// <param name="userOverride">User-specific override (null = inherit from role)</param>
        /// <returns>Effective permission value</returns>
        public static bool GetEffectivePermission(bool rolePermission, bool? userOverride)
        {
            return userOverride ?? rolePermission;
        }

        /// <summary>
        /// Creates an effective permission object from role and user permissions
        /// </summary>
        /// <param name="formName">Form name</param>
        /// <param name="rolePermission">Role permission</param>
        /// <param name="userPermission">User permission override (can be null)</param>
        /// <returns>Effective permission object</returns>
        public static EffectivePermission CreateEffectivePermission(string formName, 
            RolePermission rolePermission, UserPermission userPermission)
        {
            if (rolePermission == null)
                return null;

            return new EffectivePermission
            {
                FormName = formName,
                ReadPermission = GetEffectivePermission(rolePermission.ReadPermission, userPermission?.ReadPermission),
                NewPermission = GetEffectivePermission(rolePermission.NewPermission, userPermission?.NewPermission),
                EditPermission = GetEffectivePermission(rolePermission.EditPermission, userPermission?.EditPermission),
                DeletePermission = GetEffectivePermission(rolePermission.DeletePermission, userPermission?.DeletePermission),
                PrintPermission = GetEffectivePermission(rolePermission.PrintPermission, userPermission?.PrintPermission),
                Source = userPermission != null ? PermissionSource.UserOverride : PermissionSource.Role
            };
        }

        #endregion
    }
}
