-- Verify Forms Table Structure
-- Comprehensive verification of the forms table and its columns
-- Use this to debug the "column 'form_id' does not exist" error

-- [CheckTableExists] --
-- Check if forms table exists
SELECT EXISTS (
    SELECT FROM information_schema.tables
    WHERE table_schema = 'public'
    AND table_name = 'forms'
) as table_exists;
-- [End] --

-- [GetTableStructure] --
-- Get complete table structure with column details
SELECT 
    column_name,
    data_type,
    is_nullable,
    column_default,
    ordinal_position
FROM information_schema.columns 
WHERE table_schema = 'public'
  AND table_name = 'forms'
ORDER BY ordinal_position;
-- [End] --

-- [GetTableConstraints] --
-- Get table constraints (primary keys, foreign keys, etc.)
SELECT 
    constraint_name,
    constraint_type,
    column_name
FROM information_schema.table_constraints tc
JOIN information_schema.constraint_column_usage ccu 
    ON tc.constraint_name = ccu.constraint_name
WHERE tc.table_schema = 'public'
  AND tc.table_name = 'forms'
ORDER BY constraint_type, constraint_name;
-- [End] --

-- [GetSampleData] --
-- Get sample data from forms table (if it exists and has data)
SELECT 
    form_id,
    form_name,
    display_name,
    category,
    is_active,
    last_modified
FROM forms 
ORDER BY form_id
LIMIT 5;
-- [End] --

-- [TestFormIdColumn] --
-- Test if form_id column specifically exists and can be queried
SELECT column_name, data_type 
FROM information_schema.columns 
WHERE table_schema = 'public'
  AND table_name = 'forms'
  AND column_name = 'form_id';
-- [End] --

-- [CountRecords] --
-- Count total records in forms table
SELECT COUNT(*) as total_records FROM forms;
-- [End] --
