using System;
using System.ComponentModel.DataAnnotations;

namespace ProManage.Modules.Models.PermissionManagementForm
{
    #region Role Models

    /// <summary>
    /// Represents a role in the RBAC system
    /// </summary>
    public class Role
    {
        /// <summary>
        /// Primary key for the role
        /// </summary>
        public int RoleId { get; set; }

        /// <summary>
        /// Name of the role (must be unique)
        /// </summary>
        [Required(ErrorMessage = "Role name is required")]
        [StringLength(50, ErrorMessage = "Role name cannot exceed 50 characters")]
        public string RoleName { get; set; }

        /// <summary>
        /// Description of the role and its purpose
        /// </summary>
        [StringLength(255, ErrorMessage = "Description cannot exceed 255 characters")]
        public string Description { get; set; }

        /// <summary>
        /// Whether this role is active and can be assigned to users
        /// </summary>
        public bool IsActive { get; set; }

        /// <summary>
        /// Date when this role was created
        /// </summary>
        public DateTime CreatedDate { get; set; }

        /// <summary>
        /// Date when this role was last modified (only set when edited and saved)
        /// </summary>
        public DateTime? ModifyDate { get; set; }

        /// <summary>
        /// Constructor
        /// </summary>
        public Role()
        {
            IsActive = true;
            CreatedDate = DateTime.Now;
            // ModifyDate should be null on creation, only set when edited and saved
        }



        /// <summary>
        /// Returns a string representation of the role
        /// </summary>
        /// <returns>Role name</returns>
        public override string ToString()
        {
            return RoleName ?? string.Empty;
        }
    }

    #endregion
}
