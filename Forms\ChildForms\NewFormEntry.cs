using System;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using ProManage.Modules.Models.FormManagement;
using ProManage.Modules.Data.FormManagement;
using ProManage.Modules.Helpers.FormManagement;
using System.Diagnostics;

namespace ProManage.Forms.ChildForms
{
    /// <summary>
    /// Form for creating new forms and editing existing forms
    /// Handles both NEW and EDIT modes
    /// </summary>
    public partial class NewFormEntry : XtraForm
    {
        #region Private Fields

        private FormManagementModel _currentForm;
        private bool _isEditMode;
        private int _editFormId;

        #endregion

        #region Constructor

        /// <summary>
        /// Constructor for NEW mode
        /// </summary>
        public NewFormEntry()
        {
            InitializeComponent();
            _isEditMode = false;
            _editFormId = 0;
            _currentForm = new FormManagementModel();
            
            SetupForm();
            Debug.WriteLine("NewFormEntry initialized in NEW mode");
        }

        /// <summary>
        /// Constructor for EDIT mode
        /// </summary>
        /// <param name="formToEdit">Form model to edit</param>
        public NewFormEntry(FormManagementModel formToEdit)
        {
            InitializeComponent();
            _isEditMode = true;
            _editFormId = formToEdit.FormId;
            _currentForm = formToEdit;
            
            SetupForm();
            LoadFormData();
            Debug.WriteLine($"NewFormEntry initialized in EDIT mode for form ID: {_editFormId}");
        }

        #endregion

        #region Form Setup

        /// <summary>
        /// Sets up the form based on mode (NEW/EDIT)
        /// </summary>
        private void SetupForm()
        {
            try
            {
                // Set form title based on mode
                this.Text = _isEditMode ? "Edit Form" : "New Form";
                
                // Set form properties
                this.StartPosition = FormStartPosition.CenterParent;
                this.FormBorderStyle = FormBorderStyle.FixedDialog;
                this.MaximizeBox = false;
                this.MinimizeBox = false;
                this.ShowInTaskbar = false;
                
                // Set button text based on mode
                btnSave.Text = _isEditMode ? "Update" : "Save";
                
                Debug.WriteLine($"Form setup completed for {(_isEditMode ? "EDIT" : "NEW")} mode");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in SetupForm: {ex.Message}");
                MessageBox.Show($"Error setting up form: {ex.Message}", "Setup Error", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Loads form data for EDIT mode
        /// </summary>
        private void LoadFormData()
        {
            try
            {
                if (_isEditMode && _currentForm != null)
                {
                    txtFormName.Text = _currentForm.FormName ?? "";
                    txtDisplayName.Text = _currentForm.DisplayName ?? "";
                    txtCategory.Text = _currentForm.Category ?? "";
                    chkIsActive.Checked = _currentForm.IsActive;
                    
                    Debug.WriteLine($"Form data loaded for editing: {_currentForm.FormName}");
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in LoadFormData: {ex.Message}");
                MessageBox.Show($"Error loading form data: {ex.Message}", "Load Error", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion

        #region Event Handlers

        /// <summary>
        /// Handles Save/Update button click
        /// </summary>
        private void btnSave_Click(object sender, EventArgs e)
        {
            try
            {
                Debug.WriteLine($"Save button clicked in {(_isEditMode ? "EDIT" : "NEW")} mode");

                // Validate input
                if (!ValidateInput())
                {
                    return;
                }

                // Create/update form model
                UpdateFormModel();

                // Save to database
                bool success = SaveForm();

                if (success)
                {
                    string message = _isEditMode ? "Form updated successfully!" : "Form created successfully!";
                    MessageBox.Show(message, "Success", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    
                    this.DialogResult = DialogResult.OK;
                    this.Close();
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in btnSave_Click: {ex.Message}");
                MessageBox.Show($"Error saving form: {ex.Message}", "Save Error", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Handles Cancel button click
        /// </summary>
        private void btnCancel_Click(object sender, EventArgs e)
        {
            try
            {
                Debug.WriteLine("Cancel button clicked");
                this.DialogResult = DialogResult.Cancel;
                this.Close();
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in btnCancel_Click: {ex.Message}");
            }
        }

        #endregion

        #region Validation and Save

        /// <summary>
        /// Validates user input
        /// </summary>
        /// <returns>True if valid, false otherwise</returns>
        private bool ValidateInput()
        {
            try
            {
                // Form Name is required
                if (string.IsNullOrWhiteSpace(txtFormName.Text))
                {
                    MessageBox.Show("Form Name is required.", "Validation Error", 
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtFormName.Focus();
                    return false;
                }

                // Form Name format validation
                if (!FormManagementModel.IsValidFormNameFormat(txtFormName.Text.Trim()))
                {
                    MessageBox.Show("Form Name must start with a letter and contain only letters, numbers, and underscores.", 
                        "Validation Error", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtFormName.Focus();
                    return false;
                }

                Debug.WriteLine("Input validation passed");
                return true;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in ValidateInput: {ex.Message}");
                MessageBox.Show($"Error validating input: {ex.Message}", "Validation Error", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
                return false;
            }
        }

        /// <summary>
        /// Updates the form model with user input
        /// </summary>
        private void UpdateFormModel()
        {
            try
            {
                _currentForm.FormName = txtFormName.Text.Trim();
                _currentForm.DisplayName = string.IsNullOrWhiteSpace(txtDisplayName.Text) ? null : txtDisplayName.Text.Trim();
                _currentForm.Category = string.IsNullOrWhiteSpace(txtCategory.Text) ? null : txtCategory.Text.Trim();
                _currentForm.IsActive = chkIsActive.Checked;

                if (_isEditMode)
                {
                    _currentForm.FormId = _editFormId;
                }

                Debug.WriteLine($"Form model updated: {_currentForm.FormName}");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in UpdateFormModel: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Saves the form to database
        /// </summary>
        /// <returns>True if successful</returns>
        private bool SaveForm()
        {
            try
            {
                if (_isEditMode)
                {
                    // Update existing form
                    var updatedForm = FormManagementRepository.UpdateForm(_currentForm);
                    _currentForm = updatedForm;
                    Debug.WriteLine($"Form updated successfully: ID {_currentForm.FormId}");
                }
                else
                {
                    // Create new form
                    var createdForm = FormManagementRepository.CreateForm(_currentForm);
                    _currentForm = createdForm;
                    Debug.WriteLine($"Form created successfully: ID {_currentForm.FormId}");
                }

                return true;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in SaveForm: {ex.Message}");
                throw;
            }
        }

        #endregion

        #region Public Properties

        /// <summary>
        /// Gets the current form model (useful after save)
        /// </summary>
        public FormManagementModel CurrentForm
        {
            get { return _currentForm; }
        }

        /// <summary>
        /// Gets whether the form is in edit mode
        /// </summary>
        public bool IsEditMode
        {
            get { return _isEditMode; }
        }

        #endregion
    }
}
