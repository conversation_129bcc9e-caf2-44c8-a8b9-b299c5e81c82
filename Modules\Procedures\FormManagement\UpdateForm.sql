-- Update Existing Form
-- Updates an existing form and returns the updated record
-- Note: Basic validation is now handled in the C# application layer for better error handling

UPDATE forms SET
    form_name = TRIM(@form_name),
    display_name = CASE WHEN @display_name IS NULL OR TRIM(@display_name) = ''
                       THEN NULL
                       ELSE TRIM(@display_name)
                  END,
    category = CASE WHEN @category IS NULL OR TRIM(@category) = ''
                   THEN NULL
                   ELSE TRIM(@category)
              END,
    is_active = COALESCE(@is_active, is_active),
    last_modified = CURRENT_TIMESTAMP
WHERE form_id = @form_id
RETURNING
    form_id,
    form_name,
    display_name,
    category,
    is_active,
    last_modified;
