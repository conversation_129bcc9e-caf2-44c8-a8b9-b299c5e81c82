-- Validate Form Name
-- Checks if a form name is available for use (not already taken)
-- Parameters: @form_name (string), @exclude_form_id (int, optional)
-- Returns: status ('AVAILABLE' or 'TAKEN')

SELECT 
    CASE 
        WHEN COUNT(*) = 0 THEN 'AVAILABLE'
        ELSE 'TAKEN'
    END as status
FROM forms 
WHERE LOWER(form_name) = LOWER(@form_name)
  AND (COALESCE(@exclude_form_id, 0) = 0 OR form_id != @exclude_form_id);
