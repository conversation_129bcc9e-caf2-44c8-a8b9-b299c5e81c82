using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace ProManage.Forms.ReusableForms
{
    /// <summary>
    /// NewFormEntryForm - A child form for FormManagement that handles form entry and editing
    /// Used for both creating new forms and editing existing form records
    /// </summary>
    public partial class NewFormEntryForm : Form
    {
        #region Public Properties

        /// <summary>
        /// Gets or sets the Form Name
        /// </summary>
        public string FormName
        {
            get { return txtFormName.Text.Trim(); }
            set { txtFormName.Text = value ?? string.Empty; }
        }

        /// <summary>
        /// Gets or sets the Display Name
        /// </summary>
        public string DisplayName
        {
            get { return txtDisplayName.Text.Trim(); }
            set { txtDisplayName.Text = value ?? string.Empty; }
        }

        /// <summary>
        /// Gets or sets the Category
        /// </summary>
        public string Category
        {
            get { return cboCategory.Text; }
            set { cboCategory.Text = value ?? string.Empty; }
        }

        /// <summary>
        /// Gets or sets the IsActive status
        /// </summary>
        public bool IsActive
        {
            get { return chkIsActive.Checked; }
            set { chkIsActive.Checked = value; }
        }

        /// <summary>
        /// Gets whether the form is in Edit mode (true) or New mode (false)
        /// </summary>
        public bool IsEditMode { get; private set; }

        /// <summary>
        /// Gets the ID of the form being edited (0 for new forms)
        /// </summary>
        public int FormId { get; private set; }

        #endregion

        #region Constructors

        /// <summary>
        /// Default constructor for new form entry
        /// </summary>
        public NewFormEntryForm()
        {
            InitializeComponent();
            InitializeNewFormMode();
        }

        /// <summary>
        /// Constructor for editing an existing form
        /// </summary>
        /// <param name="formId">The ID of the form to edit</param>
        /// <param name="formName">Current form name</param>
        /// <param name="displayName">Current display name</param>
        /// <param name="category">Current category</param>
        /// <param name="isActive">Current active status</param>
        public NewFormEntryForm(int formId, string formName, string displayName, string category, bool isActive)
        {
            InitializeComponent();
            InitializeEditMode(formId, formName, displayName, category, isActive);
        }

        #endregion

        #region Initialization Methods

        /// <summary>
        /// Initializes the form for new form entry mode
        /// </summary>
        private void InitializeNewFormMode()
        {
            IsEditMode = false;
            FormId = 0;
            lblTitle.Text = "New Form Entry";
            this.Text = "New Form Entry - ProManage";
            
            // Set default values
            FormName = string.Empty;
            DisplayName = string.Empty;
            Category = string.Empty;
            IsActive = true;
        }

        /// <summary>
        /// Initializes the form for edit mode
        /// </summary>
        /// <param name="formId">Form ID</param>
        /// <param name="formName">Form name</param>
        /// <param name="displayName">Display name</param>
        /// <param name="category">Category</param>
        /// <param name="isActive">Active status</param>
        private void InitializeEditMode(int formId, string formName, string displayName, string category, bool isActive)
        {
            IsEditMode = true;
            FormId = formId;
            lblTitle.Text = "Edit Form";
            this.Text = "Edit Form - ProManage";
            
            // Set current values
            FormName = formName;
            DisplayName = displayName;
            Category = category;
            IsActive = isActive;
        }

        #endregion

        #region Public Methods

        /// <summary>
        /// Clears all form fields to their default values
        /// </summary>
        public void ClearForm()
        {
            FormName = string.Empty;
            DisplayName = string.Empty;
            Category = string.Empty;
            IsActive = true;
            HideValidationError();
        }

        /// <summary>
        /// Shows validation error message
        /// </summary>
        /// <param name="message">Error message to display</param>
        public void ShowValidationError(string message = null)
        {
            lblFormNameValidation.Text = message ?? "Must start with letter and contain only letters, numbers, and underscores";
            lblFormNameValidation.Visible = true;
        }

        /// <summary>
        /// Hides validation error message
        /// </summary>
        public void HideValidationError()
        {
            lblFormNameValidation.Visible = false;
        }

        /// <summary>
        /// Validates the form data
        /// </summary>
        /// <returns>True if valid, false otherwise</returns>
        public bool ValidateFormData()
        {
            // Reset validation display
            HideValidationError();

            // Check if form name is empty
            if (string.IsNullOrWhiteSpace(FormName))
            {
                ShowValidationError("Form name is required");
                txtFormName.Focus();
                return false;
            }

            // Check form name format (basic validation)
            if (!System.Text.RegularExpressions.Regex.IsMatch(FormName, @"^[A-Za-z][A-Za-z0-9_]*$"))
            {
                ShowValidationError("Form name must start with a letter and contain only letters, numbers, and underscores");
                txtFormName.Focus();
                return false;
            }

            return true;
        }

        #endregion
    }
}
