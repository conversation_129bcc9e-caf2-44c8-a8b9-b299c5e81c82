// FormManagement Repository - Data access layer for Form Management operations
// Usage: Provides database operations for form management with proper error handling

using System;
using System.Collections.Generic;
using System.Data;
using System.Diagnostics;
using System.Linq;
using ProManage.Modules.Models.FormManagement;
using ProManage.Modules.Connections;
using Npgsql;

namespace ProManage.Modules.Data.FormManagement
{
    /// <summary>
    /// Repository class for Form Management database operations
    /// Provides CRUD operations with proper error handling and transaction management
    /// </summary>
    public static class FormManagementRepository
    {
        #region Get Operations

        /// <summary>
        /// Retrieves all forms from the database
        /// </summary>
        /// <returns>List of FormManagementModel objects</returns>
        public static List<FormManagementModel> GetAllForms()
        {
            var forms = new List<FormManagementModel>();

            try
            {
                Debug.WriteLine("=== FormManagementRepository.GetAllForms: Starting ===");

                var result = QueryExecutor.ExecuteQueryFromFile("FormManagement", "GetAllForms", out string errorMessage, null);

                if (!string.IsNullOrEmpty(errorMessage))
                {
                    Debug.WriteLine($"Error in GetAllForms: {errorMessage}");
                    throw new Exception($"Database error: {errorMessage}");
                }

                if (result != null && result.Rows.Count > 0)
                {
                    foreach (DataRow row in result.Rows)
                    {
                        var form = FormManagementModel.FromDataRow(row);
                        if (form != null)
                        {
                            forms.Add(form);
                        }
                    }
                }

                Debug.WriteLine($"Retrieved {forms.Count} forms from database");
                return forms;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in GetAllForms: {ex.Message}");
                throw new Exception($"Failed to retrieve forms: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Retrieves a specific form by ID
        /// </summary>
        /// <param name="formId">Form ID to retrieve</param>
        /// <returns>FormManagementModel or null if not found</returns>
        public static FormManagementModel GetFormById(int formId)
        {
            try
            {
                Debug.WriteLine($"=== FormManagementRepository.GetFormById: {formId} ===");
                
                var parameters = new Dictionary<string, object>
                {
                    { "@form_id", formId }
                };
                
                var result = QueryExecutor.ExecuteQueryFromFile("FormManagement", "GetFormById", out string errorMessage, parameters);
                
                if (!string.IsNullOrEmpty(errorMessage))
                {
                    Debug.WriteLine($"Error in GetFormById: {errorMessage}");
                    throw new Exception($"Database error: {errorMessage}");
                }
                
                if (result != null && result.Rows.Count > 0)
                {
                    return FormManagementModel.FromDataRow(result.Rows[0]);
                }
                
                Debug.WriteLine($"Form with ID {formId} not found");
                return null;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in GetFormById: {ex.Message}");
                throw new Exception($"Failed to retrieve form: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Retrieves only active forms
        /// </summary>
        /// <returns>List of active FormManagementModel objects</returns>
        public static List<FormManagementModel> GetActiveForms()
        {
            var forms = new List<FormManagementModel>();
            
            try
            {
                Debug.WriteLine("=== FormManagementRepository.GetActiveForms: Starting ===");
                
                var result = QueryExecutor.ExecuteQueryFromFile("FormManagement", "GetActiveForms", out string errorMessage, null);
                
                if (!string.IsNullOrEmpty(errorMessage))
                {
                    Debug.WriteLine($"Error in GetActiveForms: {errorMessage}");
                    throw new Exception($"Database error: {errorMessage}");
                }
                
                if (result != null && result.Rows.Count > 0)
                {
                    foreach (DataRow row in result.Rows)
                    {
                        var form = FormManagementModel.FromDataRow(row);
                        if (form != null)
                        {
                            forms.Add(form);
                        }
                    }
                }
                
                Debug.WriteLine($"Retrieved {forms.Count} active forms from database");
                return forms;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in GetActiveForms: {ex.Message}");
                throw new Exception($"Failed to retrieve active forms: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Retrieves forms by category
        /// </summary>
        /// <param name="category">Category to filter by</param>
        /// <returns>List of FormManagementModel objects in the specified category</returns>
        public static List<FormManagementModel> GetFormsByCategory(string category)
        {
            var forms = new List<FormManagementModel>();
            
            try
            {
                Debug.WriteLine($"=== FormManagementRepository.GetFormsByCategory: {category} ===");
                
                var parameters = new Dictionary<string, object>
                {
                    { "@category", category }
                };
                
                var result = QueryExecutor.ExecuteQueryFromFile("FormManagement", "GetFormsByCategory", out string errorMessage, parameters);
                
                if (!string.IsNullOrEmpty(errorMessage))
                {
                    Debug.WriteLine($"Error in GetFormsByCategory: {errorMessage}");
                    throw new Exception($"Database error: {errorMessage}");
                }
                
                if (result != null && result.Rows.Count > 0)
                {
                    foreach (DataRow row in result.Rows)
                    {
                        var form = FormManagementModel.FromDataRow(row);
                        if (form != null)
                        {
                            forms.Add(form);
                        }
                    }
                }
                
                Debug.WriteLine($"Retrieved {forms.Count} forms in category '{category}' from database");
                return forms;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in GetFormsByCategory: {ex.Message}");
                throw new Exception($"Failed to retrieve forms by category: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Retrieves distinct categories from the database
        /// </summary>
        /// <returns>List of category names</returns>
        public static List<string> GetCategories()
        {
            var categories = new List<string>();
            
            try
            {
                Debug.WriteLine("=== FormManagementRepository.GetCategories: Starting ===");
                
                var result = QueryExecutor.ExecuteQueryFromFile("FormManagement", "GetCategories", out string errorMessage, null);
                
                if (!string.IsNullOrEmpty(errorMessage))
                {
                    Debug.WriteLine($"Error in GetCategories: {errorMessage}");
                    throw new Exception($"Database error: {errorMessage}");
                }
                
                if (result != null && result.Rows.Count > 0)
                {
                    foreach (DataRow row in result.Rows)
                    {
                        var category = row["category"]?.ToString();
                        if (!string.IsNullOrWhiteSpace(category))
                        {
                            categories.Add(category);
                        }
                    }
                }
                
                Debug.WriteLine($"Retrieved {categories.Count} categories from database");
                return categories;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in GetCategories: {ex.Message}");
                throw new Exception($"Failed to retrieve categories: {ex.Message}", ex);
            }
        }

        #endregion

        #region Validation Operations

        /// <summary>
        /// Validates if a form name is unique
        /// </summary>
        /// <param name="formName">Form name to validate</param>
        /// <param name="excludeFormId">Form ID to exclude from check (for updates)</param>
        /// <returns>True if form name is available</returns>
        public static bool IsFormNameAvailable(string formName, int? excludeFormId = null)
        {
            try
            {
                Debug.WriteLine($"=== FormManagementRepository.IsFormNameAvailable: {formName} ===");
                
                var parameters = new Dictionary<string, object>
                {
                    { "@form_name", formName },
                    { "@exclude_form_id", excludeFormId }
                };
                
                var result = QueryExecutor.ExecuteQueryFromFile("FormManagement", "ValidateFormName", out string errorMessage, parameters);
                
                if (!string.IsNullOrEmpty(errorMessage))
                {
                    Debug.WriteLine($"Error in IsFormNameAvailable: {errorMessage}");
                    throw new Exception($"Database error: {errorMessage}");
                }
                
                if (result != null && result.Rows.Count > 0)
                {
                    var status = result.Rows[0]["status"]?.ToString();
                    var isAvailable = status == "AVAILABLE";
                    Debug.WriteLine($"Form name '{formName}' availability: {isAvailable}");
                    return isAvailable;
                }
                
                return false;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in IsFormNameAvailable: {ex.Message}");
                throw new Exception($"Failed to validate form name: {ex.Message}", ex);
            }
        }

        #endregion

        #region Create, Update, Delete Operations

        /// <summary>
        /// Creates a new form in the database
        /// </summary>
        /// <param name="form">Form model to create</param>
        /// <returns>Created form with updated ID and timestamp</returns>
        public static FormManagementModel CreateForm(FormManagementModel form)
        {
            try
            {
                Debug.WriteLine($"=== FormManagementRepository.CreateForm: {form.FormName} ===");

                // Validate the model
                if (!form.IsValid())
                {
                    var errors = string.Join(", ", form.GetValidationErrors());
                    throw new ArgumentException($"Invalid form data: {errors}");
                }

                // Normalize the data
                form.Normalize();

                // Additional validation: Check for duplicate form name
                if (!IsFormNameAvailable(form.FormName))
                {
                    throw new ArgumentException($"A form with the name '{form.FormName}' already exists. Please choose a different name.");
                }

                // Validate form name format
                if (!FormManagementModel.IsValidFormNameFormat(form.FormName))
                {
                    throw new ArgumentException("Form name must start with a letter and contain only letters, numbers, and underscores.");
                }

                var parameters = new Dictionary<string, object>
                {
                    { "@form_name", form.FormName },
                    { "@display_name", form.DisplayName },
                    { "@category", form.Category },
                    { "@is_active", form.IsActive }
                };

                var result = QueryExecutor.ExecuteQueryFromFile("FormManagement", "InsertForm", out string errorMessage, parameters);

                if (!string.IsNullOrEmpty(errorMessage))
                {
                    Debug.WriteLine($"Error in CreateForm: {errorMessage}");

                    // Handle specific database constraint errors
                    if (errorMessage.Contains("duplicate key") || errorMessage.Contains("unique constraint"))
                    {
                        throw new ArgumentException($"A form with the name '{form.FormName}' already exists. Please choose a different name.");
                    }

                    throw new Exception($"Database error: {errorMessage}");
                }

                if (result != null && result.Rows.Count > 0)
                {
                    var createdForm = FormManagementModel.FromDataRow(result.Rows[0]);
                    Debug.WriteLine($"Form created successfully with ID: {createdForm.FormId}");
                    return createdForm;
                }

                throw new Exception("Form creation failed - no data returned");
            }
            catch (ArgumentException)
            {
                // Re-throw validation errors as-is
                throw;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in CreateForm: {ex.Message}");
                throw new Exception($"Failed to create form: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Updates an existing form in the database
        /// </summary>
        /// <param name="form">Form model with updated data</param>
        /// <returns>Updated form model</returns>
        public static FormManagementModel UpdateForm(FormManagementModel form)
        {
            try
            {
                Debug.WriteLine($"=== FormManagementRepository.UpdateForm: {form.FormId} ===");

                // Validate the model
                if (!form.IsValid())
                {
                    var errors = string.Join(", ", form.GetValidationErrors());
                    throw new ArgumentException($"Invalid form data: {errors}");
                }

                // Normalize the data
                form.Normalize();

                var parameters = new Dictionary<string, object>
                {
                    { "@form_id", form.FormId },
                    { "@form_name", form.FormName },
                    { "@display_name", form.DisplayName },
                    { "@category", form.Category },
                    { "@is_active", form.IsActive }
                };

                var result = QueryExecutor.ExecuteQueryFromFile("FormManagement", "UpdateForm", out string errorMessage, parameters);

                if (!string.IsNullOrEmpty(errorMessage))
                {
                    Debug.WriteLine($"Error in UpdateForm: {errorMessage}");
                    throw new Exception($"Database error: {errorMessage}");
                }

                if (result != null && result.Rows.Count > 0)
                {
                    var updatedForm = FormManagementModel.FromDataRow(result.Rows[0]);
                    Debug.WriteLine($"Form updated successfully: {updatedForm.FormId}");
                    return updatedForm;
                }

                throw new Exception("Form update failed - no data returned");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in UpdateForm: {ex.Message}");
                throw new Exception($"Failed to update form: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Deactivates a form (soft delete)
        /// </summary>
        /// <param name="formId">ID of form to deactivate</param>
        /// <returns>True if successful</returns>
        public static bool DeactivateForm(int formId)
        {
            try
            {
                Debug.WriteLine($"=== FormManagementRepository.DeactivateForm: {formId} ===");

                var parameters = new Dictionary<string, object>
                {
                    { "@form_id", formId }
                };

                var result = QueryExecutor.ExecuteQueryFromFile("FormManagement", "DeactivateForm", out string errorMessage, parameters);

                if (!string.IsNullOrEmpty(errorMessage))
                {
                    Debug.WriteLine($"Error in DeactivateForm: {errorMessage}");
                    throw new Exception($"Database error: {errorMessage}");
                }

                Debug.WriteLine($"Form {formId} deactivated successfully");
                return true;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in DeactivateForm: {ex.Message}");
                throw new Exception($"Failed to deactivate form: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Activates a form
        /// </summary>
        /// <param name="formId">ID of form to activate</param>
        /// <returns>True if successful</returns>
        public static bool ActivateForm(int formId)
        {
            try
            {
                Debug.WriteLine($"=== FormManagementRepository.ActivateForm: {formId} ===");

                var parameters = new Dictionary<string, object>
                {
                    { "@form_id", formId }
                };

                var result = QueryExecutor.ExecuteQueryFromFile("FormManagement", "ActivateForm", out string errorMessage, parameters);

                if (!string.IsNullOrEmpty(errorMessage))
                {
                    Debug.WriteLine($"Error in ActivateForm: {errorMessage}");
                    throw new Exception($"Database error: {errorMessage}");
                }

                Debug.WriteLine($"Form {formId} activated successfully");
                return true;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in ActivateForm: {ex.Message}");
                throw new Exception($"Failed to activate form: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Permanently deletes a form from the database
        /// </summary>
        /// <param name="formId">ID of form to delete</param>
        /// <returns>True if successful</returns>
        public static bool DeleteForm(int formId)
        {
            try
            {
                Debug.WriteLine($"=== FormManagementRepository.DeleteForm: {formId} ===");
                Debug.WriteLine($"Form ID type: {formId.GetType()}, Value: {formId}");

                // First, verify the forms table exists using a simple query
                Debug.WriteLine("Verifying forms table structure...");
                try
                {
                    string checkTableQuery = @"
                        SELECT EXISTS (
                            SELECT FROM information_schema.tables
                            WHERE table_schema = 'public'
                            AND table_name = 'forms'
                        );";

                    var verifyResult = QueryExecutor.ExecuteSelectQuery(checkTableQuery, out string verifyError);
                    if (!string.IsNullOrEmpty(verifyError))
                    {
                        Debug.WriteLine($"Table verification error: {verifyError}");
                        throw new Exception($"Cannot verify forms table: {verifyError}");
                    }
                    else if (verifyResult != null && verifyResult.Rows.Count > 0)
                    {
                        bool tableExists = Convert.ToBoolean(verifyResult.Rows[0][0]);
                        Debug.WriteLine($"Forms table exists: {tableExists}");

                        if (!tableExists)
                        {
                            throw new Exception("Forms table does not exist in the database");
                        }
                    }
                }
                catch (Exception verifyEx)
                {
                    Debug.WriteLine($"Error verifying table: {verifyEx.Message}");
                    throw new Exception($"Database verification failed: {verifyEx.Message}");
                }

                // Check if the specific form exists before attempting deletion
                Debug.WriteLine($"Checking if form {formId} exists...");
                try
                {
                    var checkParams = new Dictionary<string, object> { { "@form_id", formId } };
                    var checkResult = QueryExecutor.ExecuteQueryFromFile("FormManagement", "GetFormById", out string checkError, checkParams);

                    if (!string.IsNullOrEmpty(checkError))
                    {
                        Debug.WriteLine($"Error checking form existence: {checkError}");
                        throw new Exception($"Error verifying form exists: {checkError}");
                    }

                    if (checkResult == null || checkResult.Rows.Count == 0)
                    {
                        Debug.WriteLine($"Form {formId} does not exist in database");
                        throw new Exception($"Form with ID {formId} does not exist");
                    }

                    Debug.WriteLine($"Form {formId} exists, proceeding with deletion");
                }
                catch (Exception checkEx)
                {
                    Debug.WriteLine($"Error during form existence check: {checkEx.Message}");
                    throw new Exception($"Failed to verify form exists: {checkEx.Message}");
                }

                var parameters = new Dictionary<string, object>
                {
                    { "@form_id", formId }
                };

                Debug.WriteLine($"Executing DeleteForm with parameters: {string.Join(", ", parameters.Select(p => $"{p.Key}={p.Value}"))}");

                var result = QueryExecutor.ExecuteQueryFromFile("FormManagement", "DeleteForm", out string errorMessage, parameters);

                if (!string.IsNullOrEmpty(errorMessage))
                {
                    Debug.WriteLine($"Error in DeleteForm: {errorMessage}");
                    throw new Exception($"Database error: {errorMessage}");
                }

                Debug.WriteLine($"Form {formId} deleted successfully");
                return true;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in DeleteForm: {ex.Message}");
                throw new Exception($"Failed to delete form: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Debug method to test form deletion with simplified procedure
        /// </summary>
        /// <param name="formId">ID of form to test</param>
        /// <returns>Debug information</returns>
        public static string TestDeleteForm(int formId)
        {
            try
            {
                Debug.WriteLine($"=== Testing DeleteForm for ID: {formId} ===");

                // Use a simple direct query to test if the form exists and can be found
                string testQuery = @"
                    SELECT
                        form_id,
                        form_name,
                        'Form found for deletion' as status
                    FROM forms
                    WHERE form_id = @form_id;";

                var parameters = new Dictionary<string, object>
                {
                    { "@form_id", formId }
                };

                var result = QueryExecutor.ExecuteSelectQuery(testQuery, out string errorMessage, parameters);

                if (!string.IsNullOrEmpty(errorMessage))
                {
                    return $"ERROR: {errorMessage}";
                }

                if (result != null && result.Rows.Count > 0)
                {
                    var row = result.Rows[0];
                    return $"SUCCESS: Found form {row["form_id"]} - {row["form_name"]} - {row["status"]}";
                }

                return $"No form found with ID {formId}";
            }
            catch (Exception ex)
            {
                return $"EXCEPTION: {ex.Message}";
            }
        }

        /// <summary>
        /// Debug method to verify forms table structure
        /// </summary>
        /// <returns>Table structure information</returns>
        public static string VerifyFormsTableStructure()
        {
            try
            {
                Debug.WriteLine("=== Verifying Forms Table Structure ===");

                // Check if table exists
                string checkTableQuery = @"
                    SELECT EXISTS (
                        SELECT FROM information_schema.tables
                        WHERE table_schema = 'public'
                        AND table_name = 'forms'
                    );";

                var existsResult = QueryExecutor.ExecuteSelectQuery(checkTableQuery, out string existsError);
                if (!string.IsNullOrEmpty(existsError))
                {
                    return $"ERROR checking table existence: {existsError}";
                }

                bool tableExists = existsResult != null && existsResult.Rows.Count > 0 && Convert.ToBoolean(existsResult.Rows[0][0]);
                if (!tableExists)
                {
                    return "ERROR: Forms table does not exist";
                }

                // Get table structure
                string structureQuery = @"
                    SELECT
                        column_name,
                        data_type,
                        is_nullable,
                        column_default,
                        ordinal_position
                    FROM information_schema.columns
                    WHERE table_schema = 'public'
                      AND table_name = 'forms'
                    ORDER BY ordinal_position;";

                var structureResult = QueryExecutor.ExecuteSelectQuery(structureQuery, out string structureError);
                if (!string.IsNullOrEmpty(structureError))
                {
                    return $"ERROR getting table structure: {structureError}";
                }

                var columns = new List<string>();
                if (structureResult != null)
                {
                    foreach (DataRow row in structureResult.Rows)
                    {
                        columns.Add($"{row["column_name"]} ({row["data_type"]})");
                    }
                }

                return $"SUCCESS: Table exists with columns: {string.Join(", ", columns)}";
            }
            catch (Exception ex)
            {
                return $"EXCEPTION: {ex.Message}";
            }
        }

        #endregion
    }
}
