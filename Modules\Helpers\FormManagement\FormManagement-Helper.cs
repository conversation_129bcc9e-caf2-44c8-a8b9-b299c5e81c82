// FormManagement Helper - Simplified helper methods for Form Management operations
// Usage: Provides utility methods that work with the new simplified FormManagement form

using System;
using System.Collections.Generic;
using System.Data;
using System.Diagnostics;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using ProManage.Modules.Data.FormManagement;
using ProManage.Modules.Models.FormManagement;

namespace ProManage.Modules.Helpers.FormManagement
{
    /// <summary>
    /// Helper class for FormManagement operations
    /// Simplified to work with the new clean FormManagement form architecture
    /// </summary>
    public static class FormManagementHelper
    {
        #region Utility Methods

        /// <summary>
        /// Populates a ComboBox with available categories
        /// </summary>
        /// <param name="comboBox">ComboBox to populate</param>
        public static void PopulateCategoryComboBox(ComboBoxEdit comboBox)
        {
            try
            {
                Debug.WriteLine("=== FormManagementHelper.PopulateCategoryComboBox: Starting ===");

                var categories = FormManagementRepository.GetCategories();

                comboBox.Properties.Items.Clear();
                comboBox.Properties.Items.Add(""); // Empty option

                foreach (var category in categories)
                {
                    comboBox.Properties.Items.Add(category);
                }

                Debug.WriteLine($"Category ComboBox populated with {categories.Count} categories");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in PopulateCategoryComboBox: {ex.Message}");
            }
        }

        /// <summary>
        /// Validates form name availability
        /// </summary>
        /// <param name="formName">Form name to validate</param>
        /// <param name="excludeFormId">Form ID to exclude from check</param>
        /// <returns>True if available</returns>
        public static bool ValidateFormNameAvailability(string formName, int? excludeFormId = null)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(formName))
                    return false;

                return FormManagementRepository.IsFormNameAvailable(formName, excludeFormId);
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in ValidateFormNameAvailability: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Creates a new form with validation
        /// </summary>
        /// <param name="formName">Form name</param>
        /// <param name="displayName">Display name</param>
        /// <param name="category">Category</param>
        /// <returns>Created form model</returns>
        public static FormManagementModel CreateNewForm(string formName, string displayName, string category)
        {
            try
            {
                Debug.WriteLine($"=== FormManagementHelper.CreateNewForm: {formName} ===");

                // Create new form model
                var newForm = new FormManagementModel(formName, displayName, category);

                // Validate the form
                if (!newForm.IsValid())
                {
                    var errors = string.Join(", ", newForm.GetValidationErrors());
                    throw new ArgumentException($"Invalid form data: {errors}");
                }

                // Create in database
                var createdForm = FormManagementRepository.CreateForm(newForm);
                
                Debug.WriteLine($"Form created successfully with ID: {createdForm.FormId}");
                return createdForm;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in CreateNewForm: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Updates an existing form
        /// </summary>
        /// <param name="form">Form to update</param>
        /// <returns>Updated form model</returns>
        public static FormManagementModel UpdateForm(FormManagementModel form)
        {
            try
            {
                Debug.WriteLine($"=== FormManagementHelper.UpdateForm: {form.FormId} ===");

                // Validate the form
                if (!form.IsValid())
                {
                    var errors = string.Join(", ", form.GetValidationErrors());
                    throw new ArgumentException($"Invalid form data: {errors}");
                }

                // Update in database
                var updatedForm = FormManagementRepository.UpdateForm(form);
                
                Debug.WriteLine($"Form updated successfully: {updatedForm.FormId}");
                return updatedForm;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in UpdateForm: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Deletes a form with confirmation
        /// </summary>
        /// <param name="formId">Form ID to delete</param>
        /// <param name="formName">Form name for confirmation</param>
        /// <returns>True if deleted successfully</returns>
        public static bool DeleteForm(int formId, string formName)
        {
            try
            {
                Debug.WriteLine($"=== FormManagementHelper.DeleteForm: {formId} ===");

                var confirmMessage = $"Are you sure you want to PERMANENTLY DELETE the form '{formName}'?\n\n" +
                                   "WARNING: This action cannot be undone!\n" +
                                   "All related permission data will also be removed.";

                var result = MessageBox.Show(confirmMessage, "Confirm Permanent Deletion",
                    MessageBoxButtons.YesNo, MessageBoxIcon.Warning);

                if (result == DialogResult.Yes)
                {
                    FormManagementRepository.DeleteForm(formId);
                    Debug.WriteLine($"Form deleted successfully: {formId}");
                    return true;
                }

                return false;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in DeleteForm: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Activates or deactivates a form
        /// </summary>
        /// <param name="formId">Form ID</param>
        /// <param name="activate">True to activate, false to deactivate</param>
        /// <returns>True if successful</returns>
        public static bool SetFormActiveStatus(int formId, bool activate)
        {
            try
            {
                Debug.WriteLine($"=== FormManagementHelper.SetFormActiveStatus: {formId}, Active={activate} ===");

                if (activate)
                {
                    FormManagementRepository.ActivateForm(formId);
                }
                else
                {
                    FormManagementRepository.DeactivateForm(formId);
                }

                Debug.WriteLine($"Form status updated successfully: {formId}");
                return true;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in SetFormActiveStatus: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Gets all forms from the database
        /// </summary>
        /// <returns>List of all forms</returns>
        public static List<FormManagementModel> GetAllForms()
        {
            try
            {
                Debug.WriteLine("=== FormManagementHelper.GetAllForms: Starting ===");
                
                var forms = FormManagementRepository.GetAllForms();
                
                Debug.WriteLine($"Retrieved {forms.Count} forms");
                return forms;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in GetAllForms: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Gets active forms only
        /// </summary>
        /// <returns>List of active forms</returns>
        public static List<FormManagementModel> GetActiveForms()
        {
            try
            {
                Debug.WriteLine("=== FormManagementHelper.GetActiveForms: Starting ===");
                
                var forms = FormManagementRepository.GetActiveForms();
                
                Debug.WriteLine($"Retrieved {forms.Count} active forms");
                return forms;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in GetActiveForms: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Gets forms by category
        /// </summary>
        /// <param name="category">Category to filter by</param>
        /// <returns>List of forms in the category</returns>
        public static List<FormManagementModel> GetFormsByCategory(string category)
        {
            try
            {
                Debug.WriteLine($"=== FormManagementHelper.GetFormsByCategory: {category} ===");
                
                var forms = FormManagementRepository.GetFormsByCategory(category);
                
                Debug.WriteLine($"Retrieved {forms.Count} forms in category '{category}'");
                return forms;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in GetFormsByCategory: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Gets all available categories
        /// </summary>
        /// <returns>List of categories</returns>
        public static List<string> GetCategories()
        {
            try
            {
                Debug.WriteLine("=== FormManagementHelper.GetCategories: Starting ===");
                
                var categories = FormManagementRepository.GetCategories();
                
                Debug.WriteLine($"Retrieved {categories.Count} categories");
                return categories;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in GetCategories: {ex.Message}");
                throw;
            }
        }

        #endregion
    }
}