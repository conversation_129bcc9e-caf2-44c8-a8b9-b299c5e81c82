-- Test Delete Form (Simplified)
-- Simple delete procedure for debugging the column issue
-- This version removes the DO $$ block to isolate the parameter binding issue

-- Set transaction isolation level
SET TRANSACTION ISOLATION LEVEL SERIALIZABLE;

-- Simple delete with basic validation
-- Check if form exists first
SELECT 
    form_id,
    form_name,
    'Form found for deletion' as status
FROM forms 
WHERE form_id = @form_id;

-- If the above query works, then perform the actual delete
-- DELETE FROM forms WHERE form_id = @form_id;

-- Return confirmation
-- SELECT @form_id as deleted_form_id, 'Test delete completed' as message;
