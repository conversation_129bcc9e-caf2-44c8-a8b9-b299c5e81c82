# RBAC Database Structure Blueprint

## Current Database Structure Analysis

### 1. Database Schema Overview

The current RBAC database structure is **CORRECT** and follows standard normalized database design principles.

#### Tables and Relationships:

```sql
-- Core Tables
roles (role_id, role_name, description, is_active, created_date)
users (user_id, username, full_name, email, role_id, ...)
role_permissions (perm_id, role_id, form_name, read_permission, new_permission, edit_permission, delete_permission, print_permission)
user_permissions (perm_id, user_id, form_name, read_permission, new_permission, edit_permission, delete_permission, print_permission)
```

#### Relationships:
- `users.role_id` → `roles.role_id` (Many users can have the same role)
- `role_permissions.role_id` → `roles.role_id` (One role has many form permissions)
- `user_permissions.user_id` → `users.user_id` (One user has many form permission overrides)

### 2. Why role_id exists in role_permissions table

**This is CORRECT design.** Here's why:

#### Example Data:

**roles table:**
```
role_id | role_name    | description
--------|-------------|-------------
1       | Administrator| Full access
2       | Manager      | Management access
3       | User         | Basic access
```

**role_permissions table:**
```
perm_id | role_id | form_name        | read | new | edit | delete | print
--------|---------|------------------|------|-----|------|--------|------
1       | 1       | EstimateForm     | 1    | 1   | 1    | 1      | 1
2       | 1       | DatabaseForm     | 1    | 1   | 1    | 1      | 1
3       | 2       | EstimateForm     | 1    | 1   | 1    | 0      | 1
4       | 2       | DatabaseForm     | 1    | 0   | 0    | 0      | 1
5       | 3       | EstimateForm     | 1    | 0   | 0    | 0      | 0
6       | 3       | DatabaseForm     | 0    | 0   | 0    | 0      | 0
```

**Explanation:**
- Administrator (role_id=1) has full permissions on all forms
- Manager (role_id=2) has read/new/edit/print on EstimateForm, but only read/print on DatabaseForm
- User (role_id=3) has only read on EstimateForm, no access to DatabaseForm

**The role_id column is essential** because:
1. Each role needs different permissions for each form
2. One role (e.g., Manager) appears multiple times - once per form
3. This allows granular control: Role X can have different permissions on Form A vs Form B

## Required UI Enhancements

### 1. Read Permission Dependency Logic

#### Current Issue:
Users can check Edit/Delete/Print without checking Read, which is illogical.

#### Required Behavior:
- **Read is mandatory** for all other permissions
- When Read is unchecked → automatically uncheck and disable New/Edit/Delete/Print
- When Read is checked → enable New/Edit/Delete/Print for user selection
- Prevent checking New/Edit/Delete/Print when Read is unchecked

#### Implementation Points:
```csharp
// In grid cell value changed event
private void HandleReadPermissionChange(GridView gridView, int rowIndex)
{
    bool readChecked = (bool)gridView.GetRowCellValue(rowIndex, "ReadPermission");
    
    if (!readChecked)
    {
        // Uncheck all other permissions
        gridView.SetRowCellValue(rowIndex, "NewPermission", false);
        gridView.SetRowCellValue(rowIndex, "EditPermission", false);
        gridView.SetRowCellValue(rowIndex, "DeletePermission", false);
        gridView.SetRowCellValue(rowIndex, "PrintPermission", false);
    }
}

// In grid cell validation
private void ValidatePermissionDependency(GridView gridView, int rowIndex, string fieldName, object newValue)
{
    if (fieldName != "ReadPermission" && (bool)newValue == true)
    {
        bool readChecked = (bool)gridView.GetRowCellValue(rowIndex, "ReadPermission");
        if (!readChecked)
        {
            // Prevent checking other permissions when Read is false
            e.Valid = false;
            e.ErrorText = "Read permission must be enabled first";
        }
    }
}
```

### 2. Role Deletion Functionality

#### Requirements:
- Add "Delete Role" button in Role Permissions tab
- Check if role is assigned to any users before deletion
- Show confirmation dialog with user count
- Delete role and all associated permissions if no users assigned

#### Workflow:
1. User selects a role from dropdown
2. User clicks "Delete Role" button
3. System checks: `SELECT COUNT(*) FROM users WHERE role_id = @roleId`
4. If count > 0: Show error "Cannot delete role. X users are assigned to this role."
5. If count = 0: Show confirmation "Are you sure you want to delete role 'RoleName'?"
6. If confirmed: Delete from role_permissions, then delete from roles
7. Refresh role dropdown and clear grid

#### Database Operations:
```sql
-- Check if role is assigned to users
SELECT COUNT(*) as user_count FROM users WHERE role_id = @roleId AND is_active = 1;

-- Delete role permissions (cascade will handle this automatically)
DELETE FROM role_permissions WHERE role_id = @roleId;

-- Delete role
DELETE FROM roles WHERE role_id = @roleId;
```

## Implementation Tasks

### Task 1: Update Permission Grid Logic
**Files to modify:**
- `Modules/Helpers/PermissionManagementForm/PermissionGridHelper.cs`
- `Forms/MainForms/PermissionManagementForm.cs`

**Changes needed:**
1. Add cell validation event handlers
2. Implement Read permission dependency logic
3. Add visual feedback for disabled permissions

### Task 2: Add Role Deletion Feature
**Files to modify:**
- `Forms/MainForms/PermissionManagementForm.cs` (UI)
- `Forms/MainForms/PermissionManagementForm.Designer.cs` (Button)
- `Modules/Data/PermissionManagementForm/PermissionManagementFormData.cs` (Data layer)

**Changes needed:**
1. Add "Delete Role" button to Role Permissions tab
2. Implement role usage validation
3. Add confirmation dialogs
4. Handle role deletion workflow

### Task 3: Update Database Service
**Files to modify:**
- `Modules/Data/PermissionManagementForm/PermissionManagementFormData.cs`
- `Modules/Procedures/Permissions/Permission-Queries.sql`

**New methods needed:**
```csharp
public int GetRoleUsageCount(int roleId)
public bool DeleteRole(int roleId)
public bool CanDeleteRole(int roleId)
```

### Task 4: UI Enhancement Details

#### Read Permission Dependency Implementation:
```csharp
// In PermissionGridHelper.cs
public void SetupPermissionDependencyValidation(GridView gridView)
{
    gridView.ValidatingEditor += (s, e) => ValidatePermissionDependency(s, e);
    gridView.CellValueChanged += (s, e) => HandlePermissionChange(s, e);
}

private void ValidatePermissionDependency(object sender, BaseContainerValidateEditorEventArgs e)
{
    var gridView = sender as GridView;
    if (gridView.FocusedColumn.FieldName != "ReadPermission" && (bool)e.Value == true)
    {
        bool readChecked = (bool)gridView.GetFocusedRowCellValue("ReadPermission");
        if (!readChecked)
        {
            e.Valid = false;
            e.ErrorText = "Read permission must be enabled first";
        }
    }
}

private void HandlePermissionChange(object sender, CellValueChangedEventArgs e)
{
    var gridView = sender as GridView;
    if (e.Column.FieldName == "ReadPermission" && !(bool)e.Value)
    {
        // Uncheck all other permissions when Read is unchecked
        gridView.SetRowCellValue(e.RowHandle, "NewPermission", false);
        gridView.SetRowCellValue(e.RowHandle, "EditPermission", false);
        gridView.SetRowCellValue(e.RowHandle, "DeletePermission", false);
        gridView.SetRowCellValue(e.RowHandle, "PrintPermission", false);
    }
}
```

#### Role Deletion Button Implementation:
```csharp
// In PermissionManagementForm.cs
private void btnDeleteRole_Click(object sender, EventArgs e)
{
    if (cmbRoles.EditValue == null)
    {
        MessageBox.Show("Please select a role to delete.", "No Role Selected",
            MessageBoxButtons.OK, MessageBoxIcon.Warning);
        return;
    }

    int roleId = (int)cmbRoles.EditValue;
    string roleName = cmbRoles.Text;

    // Check if role is assigned to users
    int userCount = _formData.GetRoleUsageCount(roleId);

    if (userCount > 0)
    {
        MessageBox.Show($"Cannot delete role '{roleName}'. {userCount} user(s) are assigned to this role.\n\n" +
            "Please reassign users to different roles before deleting.",
            "Role In Use", MessageBoxButtons.OK, MessageBoxIcon.Warning);
        return;
    }

    // Confirm deletion
    var result = MessageBox.Show($"Are you sure you want to delete role '{roleName}'?\n\n" +
        "This action will permanently remove the role and all its permissions.",
        "Confirm Role Deletion", MessageBoxButtons.YesNo, MessageBoxIcon.Question);

    if (result == DialogResult.Yes)
    {
        if (_formData.DeleteRole(roleId))
        {
            MessageBox.Show($"Role '{roleName}' has been successfully deleted.",
                "Role Deleted", MessageBoxButtons.OK, MessageBoxIcon.Information);
            LoadRoles(); // Refresh role dropdown
            ClearRolePermissionsGrid();
        }
        else
        {
            MessageBox.Show("Failed to delete role. Please try again.",
                "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
    }
}
```

### Task 5: Database Queries
**New SQL queries needed in Permission-Queries.sql:**

```sql
-- [GetRoleUsageCount] --
SELECT COUNT(*) as user_count
FROM users
WHERE role_id = @roleId AND is_active = true;
-- [End] --

-- [DeleteRole] --
BEGIN TRANSACTION;
    -- Delete role permissions (or rely on CASCADE)
    DELETE FROM role_permissions WHERE role_id = @roleId;

    -- Delete role
    DELETE FROM roles WHERE role_id = @roleId;
COMMIT;
-- [End] --

-- [CheckRoleExists] --
SELECT COUNT(*) as role_count
FROM roles
WHERE role_id = @roleId;
-- [End] --
```

## Database Structure Conclusion

**The current database structure is CORRECT and does not need restructuring.**

The role_id column in role_permissions table is essential for the many-to-many relationship between roles and form permissions. Each role can have different permissions for different forms, which requires the role_id to be repeated for each form-role combination.

**No database schema changes are needed** - only UI logic enhancements and additional functionality.

## Next Steps

1. **Review this blueprint** and confirm the approach
2. **Implement Task 1** (Permission dependency logic) first
3. **Implement Task 2** (Role deletion feature) second
4. **Test thoroughly** with various scenarios
5. **Update documentation** with new features
